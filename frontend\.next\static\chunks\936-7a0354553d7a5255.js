"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[936],{94936:(e,t,r)=>{r.d(t,{T:()=>P});var[n,o]=(0,r(89583).q)({name:"ButtonGroupContext",strict:!1}),a=r(84725),i=r(46611),s=r(12115),l=r(57010),u=r(67093),d=r(97262),c=r(83517),f=r(33138),p=r(54515),g=r(1206),m=r(2877),v=(0,g.tv)({base:["z-0","group","relative","inline-flex","items-center","justify-center","box-border","appearance-none","outline-none","select-none","whitespace-nowrap","min-w-max","font-normal","subpixel-antialiased","overflow-hidden","tap-highlight-transparent","data-[pressed=true]:scale-[0.97]",...m.zb],variants:{variant:{solid:"",bordered:"border-medium bg-transparent",light:"bg-transparent",flat:"",faded:"border-medium",shadow:"",ghost:"border-medium bg-transparent"},size:{sm:"px-3 min-w-16 h-8 text-tiny gap-2 rounded-small",md:"px-4 min-w-20 h-10 text-small gap-2 rounded-medium",lg:"px-6 min-w-24 h-12 text-medium gap-3 rounded-large"},color:{default:"",primary:"",secondary:"",success:"",warning:"",danger:""},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"},fullWidth:{true:"w-full"},isDisabled:{true:"opacity-disabled pointer-events-none"},isInGroup:{true:"[&:not(:first-child):not(:last-child)]:rounded-none"},isIconOnly:{true:"px-0 !gap-0",false:"[&>svg]:max-w-[theme(spacing.8)]"},disableAnimation:{true:"!transition-none data-[pressed=true]:scale-100",false:"transition-transform-colors-opacity motion-reduce:transition-none"}},defaultVariants:{size:"md",variant:"solid",color:"default",fullWidth:!1,isDisabled:!1,isInGroup:!1},compoundVariants:[{variant:"solid",color:"default",class:p.k.solid.default},{variant:"solid",color:"primary",class:p.k.solid.primary},{variant:"solid",color:"secondary",class:p.k.solid.secondary},{variant:"solid",color:"success",class:p.k.solid.success},{variant:"solid",color:"warning",class:p.k.solid.warning},{variant:"solid",color:"danger",class:p.k.solid.danger},{variant:"shadow",color:"default",class:p.k.shadow.default},{variant:"shadow",color:"primary",class:p.k.shadow.primary},{variant:"shadow",color:"secondary",class:p.k.shadow.secondary},{variant:"shadow",color:"success",class:p.k.shadow.success},{variant:"shadow",color:"warning",class:p.k.shadow.warning},{variant:"shadow",color:"danger",class:p.k.shadow.danger},{variant:"bordered",color:"default",class:p.k.bordered.default},{variant:"bordered",color:"primary",class:p.k.bordered.primary},{variant:"bordered",color:"secondary",class:p.k.bordered.secondary},{variant:"bordered",color:"success",class:p.k.bordered.success},{variant:"bordered",color:"warning",class:p.k.bordered.warning},{variant:"bordered",color:"danger",class:p.k.bordered.danger},{variant:"flat",color:"default",class:p.k.flat.default},{variant:"flat",color:"primary",class:p.k.flat.primary},{variant:"flat",color:"secondary",class:p.k.flat.secondary},{variant:"flat",color:"success",class:p.k.flat.success},{variant:"flat",color:"warning",class:p.k.flat.warning},{variant:"flat",color:"danger",class:p.k.flat.danger},{variant:"faded",color:"default",class:p.k.faded.default},{variant:"faded",color:"primary",class:p.k.faded.primary},{variant:"faded",color:"secondary",class:p.k.faded.secondary},{variant:"faded",color:"success",class:p.k.faded.success},{variant:"faded",color:"warning",class:p.k.faded.warning},{variant:"faded",color:"danger",class:p.k.faded.danger},{variant:"light",color:"default",class:[p.k.light.default,"data-[hover=true]:bg-default/40"]},{variant:"light",color:"primary",class:[p.k.light.primary,"data-[hover=true]:bg-primary/20"]},{variant:"light",color:"secondary",class:[p.k.light.secondary,"data-[hover=true]:bg-secondary/20"]},{variant:"light",color:"success",class:[p.k.light.success,"data-[hover=true]:bg-success/20"]},{variant:"light",color:"warning",class:[p.k.light.warning,"data-[hover=true]:bg-warning/20"]},{variant:"light",color:"danger",class:[p.k.light.danger,"data-[hover=true]:bg-danger/20"]},{variant:"ghost",color:"default",class:[p.k.ghost.default,"data-[hover=true]:!bg-default"]},{variant:"ghost",color:"primary",class:[p.k.ghost.primary,"data-[hover=true]:!bg-primary data-[hover=true]:!text-primary-foreground"]},{variant:"ghost",color:"secondary",class:[p.k.ghost.secondary,"data-[hover=true]:!bg-secondary data-[hover=true]:!text-secondary-foreground"]},{variant:"ghost",color:"success",class:[p.k.ghost.success,"data-[hover=true]:!bg-success data-[hover=true]:!text-success-foreground"]},{variant:"ghost",color:"warning",class:[p.k.ghost.warning,"data-[hover=true]:!bg-warning data-[hover=true]:!text-warning-foreground"]},{variant:"ghost",color:"danger",class:[p.k.ghost.danger,"data-[hover=true]:!bg-danger data-[hover=true]:!text-danger-foreground"]},{isInGroup:!0,class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,size:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,isRounded:!0,class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,radius:"none",class:"rounded-none first:rounded-s-none last:rounded-e-none"},{isInGroup:!0,radius:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,radius:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,radius:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,radius:"full",class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,variant:["ghost","bordered"],color:"default",className:m.oT.default},{isInGroup:!0,variant:["ghost","bordered"],color:"primary",className:m.oT.primary},{isInGroup:!0,variant:["ghost","bordered"],color:"secondary",className:m.oT.secondary},{isInGroup:!0,variant:["ghost","bordered"],color:"success",className:m.oT.success},{isInGroup:!0,variant:["ghost","bordered"],color:"warning",className:m.oT.warning},{isInGroup:!0,variant:["ghost","bordered"],color:"danger",className:m.oT.danger},{isIconOnly:!0,size:"sm",class:"min-w-8 w-8 h-8"},{isIconOnly:!0,size:"md",class:"min-w-10 w-10 h-10"},{isIconOnly:!0,size:"lg",class:"min-w-12 w-12 h-12"},{variant:["solid","faded","flat","bordered","shadow"],class:"data-[hover=true]:opacity-hover"}]});(0,g.tv)({base:"inline-flex items-center justify-center h-auto",variants:{fullWidth:{true:"w-full"}},defaultVariants:{fullWidth:!1}});var b=r(35130),y=r(13201),h=r(65858),w=r(72366),x=r(39867),T=r(5582),E=r(95155),k=(0,T.Rf)((e,t)=>{let{Component:r,domRef:n,children:p,styles:g,spinnerSize:m,spinner:T=(0,E.jsx)(w.o,{color:"current",size:m}),spinnerPlacement:k,startContent:P,endContent:C,isLoading:S,disableRipple:M,getButtonProps:j,getRippleProps:O,isIconOnly:I}=function(e){var t,r,n,p,g,m,w,x,T;let E=o(),k=(0,a.o)(),P=!!E,{ref:C,as:S,children:M,startContent:j,endContent:O,autoFocus:I,className:L,spinner:A,isLoading:R=!1,disableRipple:z=!1,fullWidth:N=null!=(t=null==E?void 0:E.fullWidth)&&t,radius:$=null==E?void 0:E.radius,size:F=null!=(r=null==E?void 0:E.size)?r:"md",color:D=null!=(n=null==E?void 0:E.color)?n:"default",variant:_=null!=(p=null==E?void 0:E.variant)?p:"solid",disableAnimation:K=null!=(m=null!=(g=null==E?void 0:E.disableAnimation)?g:null==k?void 0:k.disableAnimation)&&m,isDisabled:W=null!=(w=null==E?void 0:E.isDisabled)&&w,isIconOnly:V=null!=(x=null==E?void 0:E.isIconOnly)&&x,spinnerPlacement:G="start",onPress:H,onClick:B,...U}=e,Y=S||"button",X="string"==typeof Y,q=(0,c.zD)(C),J=null!=(T=z||(null==k?void 0:k.disableRipple))?T:K,{isFocusVisible:Q,isFocused:Z,focusProps:ee}=(0,l.o)({autoFocus:I}),et=W||R,er=(0,s.useMemo)(()=>v({size:F,color:D,variant:_,radius:$,fullWidth:N,isDisabled:et,isInGroup:P,disableAnimation:K,isIconOnly:V,className:L}),[F,D,_,$,N,et,P,V,K,L]),{onPress:en,onClear:eo,ripples:ea}=(0,h.k)(),ei=(0,s.useCallback)(e=>{J||et||K||!q.current||en(e)},[J,et,K,q,en]),{buttonProps:es,isPressed:el}=(0,b.l)({elementType:S,isDisabled:et,onPress:(0,u.c)(H,ei),onClick:B,...U},q),{isHovered:eu,hoverProps:ed}=(0,y.M)({isDisabled:et}),ec=(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-disabled":(0,i.sE)(et),"data-focus":(0,i.sE)(Z),"data-pressed":(0,i.sE)(el),"data-focus-visible":(0,i.sE)(Q),"data-hover":(0,i.sE)(eu),"data-loading":(0,i.sE)(R),...(0,d.v)(es,ee,ed,(0,f.$)(U,{enabled:X}),(0,f.$)(e))}},[R,et,Z,el,X,Q,eu,es,ee,ed,U]),ef=e=>(0,s.isValidElement)(e)?(0,s.cloneElement)(e,{"aria-hidden":!0,focusable:!1,tabIndex:-1}):null,ep=ef(j);return{Component:Y,children:M,domRef:q,spinner:A,styles:er,startContent:ep,endContent:ef(O),isLoading:R,spinnerPlacement:G,spinnerSize:(0,s.useMemo)(()=>({sm:"sm",md:"sm",lg:"md"})[F],[F]),disableRipple:J,getButtonProps:ec,getRippleProps:(0,s.useCallback)(()=>({ripples:ea,onClear:eo}),[ea,eo]),isIconOnly:V}}({...e,ref:t});return(0,E.jsxs)(r,{ref:n,className:g,...j(),children:[P,S&&"start"===k&&T,S&&I?null:p,S&&"end"===k&&T,C,!M&&(0,E.jsx)(x.j,{...O()})]})});k.displayName="NextUI.Button";var P=k},33138:(e,t,r)=>{r.d(t,{$:()=>l});var n=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),o=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),a=/^(data-.*)$/,i=/^(aria-.*)$/,s=/^(on[A-Z].*)$/;function l(e,t={}){let{labelable:r=!0,enabled:u=!0,propNames:d,omitPropNames:c,omitEventNames:f,omitDataProps:p,omitEventProps:g}=t,m={};if(!u)return e;for(let t in e)!((null==c?void 0:c.has(t))||(null==f?void 0:f.has(t))&&s.test(t)||s.test(t)&&!o.has(t)||p&&a.test(t)||g&&s.test(t))&&(Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&i.test(t)||(null==d?void 0:d.has(t))||a.test(t))||s.test(t))&&(m[t]=e[t]);return m}},83517:(e,t,r)=>{r.d(t,{zD:()=>o});var n=r(12115);function o(e){let t=(0,n.useRef)(null);return(0,n.useImperativeHandle)(e,()=>t.current),t}"undefined"!=typeof window&&window.document&&window.document.createElement},65858:(e,t,r)=>{r.d(t,{k:()=>a});var n=r(55078),o=r(12115);function a(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=(0,o.useState)([]),a=(0,o.useCallback)(e=>{let t=e.target,o=Math.max(t.clientWidth,t.clientHeight);r(t=>[...t,{key:(0,n.Lz)(t.length.toString()),size:o,x:e.x-o/2,y:e.y-o/2}])},[]);return{ripples:t,onClear:(0,o.useCallback)(e=>{r(t=>t.filter(t=>t.key!==e))},[]),onPress:a,...e}}},39867:(e,t,r)=>{r.d(t,{j:()=>u});var n=r(76498),o=r(25683),a=r(91307),i=r(95155),s=()=>Promise.all([r.e(446),r.e(631)]).then(r.bind(r,55631)).then(e=>e.default),l=e=>{let{ripples:t=[],motionProps:r,color:l="currentColor",style:u,onClear:d}=e;return(0,i.jsx)(i.Fragment,{children:t.map(e=>{let t=Math.min(Math.max(.01*e.size,.2),e.size>100?.75:.5);return(0,i.jsx)(n.F,{features:s,children:(0,i.jsx)(o.N,{mode:"popLayout",children:(0,i.jsx)(a.m.span,{animate:{transform:"scale(2)",opacity:0},className:"nextui-ripple",exit:{opacity:0},initial:{transform:"scale(0)",opacity:.35},style:{position:"absolute",backgroundColor:l,borderRadius:"100%",transformOrigin:"center",pointerEvents:"none",overflow:"hidden",inset:0,zIndex:0,top:e.y,left:e.x,width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),...u},transition:{duration:t},onAnimationComplete:()=>{d(e.key)},...r})})},e.key)})})};l.displayName="NextUI.Ripple";var u=l},49163:(e,t,r)=>{r.d(t,{$:()=>n});function n(...e){for(var t,r,o=0,a="";o<e.length;)(t=e[o++])&&(r=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n);else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(t))&&(a&&(a+=" "),a+=r);return a}},70427:(e,t,r)=>{r.d(t,{R:()=>o});var n={};function o(e,t,...r){let a=t?` [${t}]`:" ",i=`[Next UI]${a}: ${e}`;"undefined"!=typeof console&&(n[i]||(n[i]=!0))}},46611:(e,t,r)=>{function n(e){return Array.isArray(e)}function o(e){let t=typeof e;return null!=e&&("object"===t||"function"===t)&&!n(e)}function a(e){return n(e)?n(e)&&0===e.length:o(e)?o(e)&&0===Object.keys(e).length:null==e||""===e}function i(e){return"function"==typeof e}r.d(t,{Im:()=>a,Tn:()=>i,sE:()=>s});var s=e=>e?"true":void 0},55078:(e,t,r)=>{r.d(t,{ZH:()=>p,QA:()=>y,Lz:()=>g,t6:()=>v,GU:()=>m});var n=Object.create,o=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,u=(e,t)=>function(){return t||(0,e[i(e)[0]])((t={exports:{}}).exports,t),t.exports},d=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of i(t))l.call(e,s)||s===r||o(e,s,{get:()=>t[s],enumerable:!(n=a(t,s))||n.enumerable});return e},c=u({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react.production.min.js"(e){var t=Symbol.for("react.element"),r=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),c=Symbol.for("react.lazy"),f=Symbol.iterator,p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,m={};function v(e,t,r){this.props=e,this.context=t,this.refs=m,this.updater=r||p}function b(){}function y(e,t,r){this.props=e,this.context=t,this.refs=m,this.updater=r||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var h=y.prototype=new b;h.constructor=y,g(h,v.prototype),h.isPureReactComponent=!0;var w=Array.isArray,x=Object.prototype.hasOwnProperty,T={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function k(e,r,n){var o,a={},i=null,s=null;if(null!=r)for(o in void 0!==r.ref&&(s=r.ref),void 0!==r.key&&(i=""+r.key),r)x.call(r,o)&&!E.hasOwnProperty(o)&&(a[o]=r[o]);var l=arguments.length-2;if(1===l)a.children=n;else if(1<l){for(var u=Array(l),d=0;d<l;d++)u[d]=arguments[d+2];a.children=u}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===a[o]&&(a[o]=l[o]);return{$$typeof:t,type:e,key:i,ref:s,props:a,_owner:T.current}}function P(e){return"object"==typeof e&&null!==e&&e.$$typeof===t}var C=/\/+/g;function S(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function M(e,n,o){if(null==e)return e;var a=[],i=0;return!function e(n,o,a,i,s){var l,u,d,c=typeof n;("undefined"===c||"boolean"===c)&&(n=null);var p=!1;if(null===n)p=!0;else switch(c){case"string":case"number":p=!0;break;case"object":switch(n.$$typeof){case t:case r:p=!0}}if(p)return s=s(p=n),n=""===i?"."+S(p,0):i,w(s)?(a="",null!=n&&(a=n.replace(C,"$&/")+"/"),e(s,o,a,"",function(e){return e})):null!=s&&(P(s)&&(l=s,u=a+(!s.key||p&&p.key===s.key?"":(""+s.key).replace(C,"$&/")+"/")+n,s={$$typeof:t,type:l.type,key:u,ref:l.ref,props:l.props,_owner:l._owner}),o.push(s)),1;if(p=0,i=""===i?".":i+":",w(n))for(var g=0;g<n.length;g++){var m=i+S(c=n[g],g);p+=e(c,o,a,m,s)}else if("function"==typeof(m=null===(d=n)||"object"!=typeof d?null:"function"==typeof(d=f&&d[f]||d["@@iterator"])?d:null))for(n=m.call(n),g=0;!(c=n.next()).done;)m=i+S(c=c.value,g++),p+=e(c,o,a,m,s);else if("object"===c)throw Error("Objects are not valid as a React child (found: "+("[object Object]"===(o=String(n))?"object with keys {"+Object.keys(n).join(", ")+"}":o)+"). If you meant to render a collection of children, use an array instead.");return p}(e,a,"","",function(e){return n.call(o,e,i++)}),a}function j(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O={current:null},I={transition:null};e.Children={map:M,forEach:function(e,t,r){M(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return M(e,function(){t++}),t},toArray:function(e){return M(e,function(e){return e})||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},e.Component=v,e.Fragment=n,e.Profiler=a,e.PureComponent=y,e.StrictMode=o,e.Suspense=u,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED={ReactCurrentDispatcher:O,ReactCurrentBatchConfig:I,ReactCurrentOwner:T},e.cloneElement=function(e,r,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=g({},e.props),a=e.key,i=e.ref,s=e._owner;if(null!=r){if(void 0!==r.ref&&(i=r.ref,s=T.current),void 0!==r.key&&(a=""+r.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in r)x.call(r,u)&&!E.hasOwnProperty(u)&&(o[u]=void 0===r[u]&&void 0!==l?l[u]:r[u])}var u=arguments.length-2;if(1===u)o.children=n;else if(1<u){l=Array(u);for(var d=0;d<u;d++)l[d]=arguments[d+2];o.children=l}return{$$typeof:t,type:e.type,key:a,ref:i,props:o,_owner:s}},e.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},e.createElement=k,e.createFactory=function(e){var t=k.bind(null,e);return t.type=e,t},e.createRef=function(){return{current:null}},e.forwardRef=function(e){return{$$typeof:l,render:e}},e.isValidElement=P,e.lazy=function(e){return{$$typeof:c,_payload:{_status:-1,_result:e},_init:j}},e.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},e.startTransition=function(e){var t=I.transition;I.transition={};try{e()}finally{I.transition=t}},e.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},e.useCallback=function(e,t){return O.current.useCallback(e,t)},e.useContext=function(e){return O.current.useContext(e)},e.useDebugValue=function(){},e.useDeferredValue=function(e){return O.current.useDeferredValue(e)},e.useEffect=function(e,t){return O.current.useEffect(e,t)},e.useId=function(){return O.current.useId()},e.useImperativeHandle=function(e,t,r){return O.current.useImperativeHandle(e,t,r)},e.useInsertionEffect=function(e,t){return O.current.useInsertionEffect(e,t)},e.useLayoutEffect=function(e,t){return O.current.useLayoutEffect(e,t)},e.useMemo=function(e,t){return O.current.useMemo(e,t)},e.useReducer=function(e,t,r){return O.current.useReducer(e,t,r)},e.useRef=function(e){return O.current.useRef(e)},e.useState=function(e){return O.current.useState(e)},e.useSyncExternalStore=function(e,t,r){return O.current.useSyncExternalStore(e,t,r)},e.useTransition=function(){return O.current.useTransition()},e.version="18.2.0"}});u({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/cjs/react.development.js"(e,t){}});var f=((e,t,r)=>(r=null!=e?n(s(e)):{},d(!t&&e&&e.__esModule?r:o(r,"default",{value:e,enumerable:!0}),e)))(u({"../../../node_modules/.pnpm/react@18.2.0/node_modules/react/index.js"(e,t){t.exports=c()}})()),p=e=>e?e.charAt(0).toUpperCase()+e.slice(1).toLowerCase():"";function g(e){return`${e}-${Math.floor(1e6*Math.random())}`}function m(e){for(let t in e)t.startsWith("on")&&delete e[t];return e}function v(e){if(!e||"object"!=typeof e)return"";try{return JSON.stringify(e)}catch(e){return""}}var b=()=>"19"===f.default.version.split(".")[0],y=e=>b()?e:e?"":void 0},72366:(e,t,r)=>{r.d(t,{o:()=>d});var n=r(5582),o=(0,r(1206).tv)({slots:{base:"relative inline-flex flex-col gap-2 items-center justify-center",wrapper:"relative flex",circle1:["absolute","w-full","h-full","rounded-full","animate-spinner-ease-spin","border-2","border-solid","border-t-transparent","border-l-transparent","border-r-transparent"],circle2:["absolute","w-full","h-full","rounded-full","opacity-75","animate-spinner-linear-spin","border-2","border-dotted","border-t-transparent","border-l-transparent","border-r-transparent"],label:"text-foreground dark:text-foreground-dark font-regular"},variants:{size:{sm:{wrapper:"w-5 h-5",circle1:"border-2",circle2:"border-2",label:"text-small"},md:{wrapper:"w-8 h-8",circle1:"border-3",circle2:"border-3",label:"text-medium"},lg:{wrapper:"w-10 h-10",circle1:"border-3",circle2:"border-3",label:"text-large"}},color:{current:{circle1:"border-b-current",circle2:"border-b-current"},white:{circle1:"border-b-white",circle2:"border-b-white"},default:{circle1:"border-b-default",circle2:"border-b-default"},primary:{circle1:"border-b-primary",circle2:"border-b-primary"},secondary:{circle1:"border-b-secondary",circle2:"border-b-secondary"},success:{circle1:"border-b-success",circle2:"border-b-success"},warning:{circle1:"border-b-warning",circle2:"border-b-warning"},danger:{circle1:"border-b-danger",circle2:"border-b-danger"}},labelColor:{foreground:{label:"text-foreground"},primary:{label:"text-primary"},secondary:{label:"text-secondary"},success:{label:"text-success"},warning:{label:"text-warning"},danger:{label:"text-danger"}}},defaultVariants:{size:"md",color:"primary",labelColor:"foreground"}}),a=r(55078),i=r(49163),s=r(12115),l=r(95155),u=(0,n.Rf)((e,t)=>{let{slots:r,classNames:u,label:d,getSpinnerProps:c}=function(e){let[t,r]=(0,n.rE)(e,o.variantKeys),{children:l,className:u,classNames:d,label:c,...f}=t,p=(0,s.useMemo)(()=>o({...r}),[(0,a.t6)(r)]),g=(0,i.$)(null==d?void 0:d.base,u),m=c||l,v=(0,s.useMemo)(()=>m&&"string"==typeof m?m:f["aria-label"]?"":"Loading",[l,m,f["aria-label"]]),b=(0,s.useCallback)(()=>({"aria-label":v,className:p.base({class:g}),...f}),[v,p,g,f]);return{label:m,slots:p,classNames:d,getSpinnerProps:b}}({...e});return(0,l.jsxs)("div",{ref:t,...c(),children:[(0,l.jsxs)("div",{className:r.wrapper({class:null==u?void 0:u.wrapper}),children:[(0,l.jsx)("i",{className:r.circle1({class:null==u?void 0:u.circle1})}),(0,l.jsx)("i",{className:r.circle2({class:null==u?void 0:u.circle2})})]}),d&&(0,l.jsx)("span",{className:r.label({class:null==u?void 0:u.label}),children:d})]})});u.displayName="NextUI.Spinner";var d=u},5582:(e,t,r)=>{r.d(t,{Rf:()=>o,rE:()=>a});var n=r(12115);function o(e){return(0,n.forwardRef)(e)}var a=(e,t,r=!0)=>{if(!t)return[e,{}];let n=t.reduce((t,r)=>r in e?{...t,[r]:e[r]}:t,{});return r?[Object.keys(e).filter(e=>!t.includes(e)).reduce((t,r)=>({...t,[r]:e[r]}),{}),n]:[e,n]}},2877:(e,t,r)=>{r.d(t,{oT:()=>a,wA:()=>o,zb:()=>n});var n=["outline-none","data-[focus-visible=true]:z-10","data-[focus-visible=true]:outline-2","data-[focus-visible=true]:outline-focus","data-[focus-visible=true]:outline-offset-2"],o=["outline-none","group-data-[focus-visible=true]:z-10","group-data-[focus-visible=true]:ring-2","group-data-[focus-visible=true]:ring-focus","group-data-[focus-visible=true]:ring-offset-2","group-data-[focus-visible=true]:ring-offset-background"],a={default:["[&+.border-medium.border-default]:ms-[calc(theme(borderWidth.medium)*-1)]"],primary:["[&+.border-medium.border-primary]:ms-[calc(theme(borderWidth.medium)*-1)]"],secondary:["[&+.border-medium.border-secondary]:ms-[calc(theme(borderWidth.medium)*-1)]"],success:["[&+.border-medium.border-success]:ms-[calc(theme(borderWidth.medium)*-1)]"],warning:["[&+.border-medium.border-warning]:ms-[calc(theme(borderWidth.medium)*-1)]"],danger:["[&+.border-medium.border-danger]:ms-[calc(theme(borderWidth.medium)*-1)]"]}},45628:(e,t,r)=>{r.d(t,{w:()=>o});var n=["small","medium","large"],o={theme:{opacity:["disabled"],spacing:["divider"],borderWidth:n,borderRadius:n},classGroups:{shadow:[{shadow:n}],"font-size":[{text:["tiny",...n]}],"bg-image":["bg-stripe-gradient-default","bg-stripe-gradient-primary","bg-stripe-gradient-secondary","bg-stripe-gradient-success","bg-stripe-gradient-warning","bg-stripe-gradient-danger"]}}},54515:(e,t,r)=>{r.d(t,{k:()=>n});var n={solid:{default:"bg-default text-default-foreground",primary:"bg-primary text-primary-foreground",secondary:"bg-secondary text-secondary-foreground",success:"bg-success text-success-foreground",warning:"bg-warning text-warning-foreground",danger:"bg-danger text-danger-foreground",foreground:"bg-foreground text-background"},shadow:{default:"shadow-lg shadow-default/50 bg-default text-default-foreground",primary:"shadow-lg shadow-primary/40 bg-primary text-primary-foreground",secondary:"shadow-lg shadow-secondary/40 bg-secondary text-secondary-foreground",success:"shadow-lg shadow-success/40 bg-success text-success-foreground",warning:"shadow-lg shadow-warning/40 bg-warning text-warning-foreground",danger:"shadow-lg shadow-danger/40 bg-danger text-danger-foreground",foreground:"shadow-lg shadow-foreground/40 bg-foreground text-background"},bordered:{default:"bg-transparent border-default text-foreground",primary:"bg-transparent border-primary text-primary",secondary:"bg-transparent border-secondary text-secondary",success:"bg-transparent border-success text-success",warning:"bg-transparent border-warning text-warning",danger:"bg-transparent border-danger text-danger",foreground:"bg-transparent border-foreground text-foreground"},flat:{default:"bg-default/40 text-default-700",primary:"bg-primary/20 text-primary-600",secondary:"bg-secondary/20 text-secondary-600",success:"bg-success/20 text-success-700 dark:text-success",warning:"bg-warning/20 text-warning-700 dark:text-warning",danger:"bg-danger/20 text-danger-600 dark:text-danger-500",foreground:"bg-foreground/10 text-foreground"},faded:{default:"border-default bg-default-100 text-default-foreground",primary:"border-default bg-default-100 text-primary",secondary:"border-default bg-default-100 text-secondary",success:"border-default bg-default-100 text-success",warning:"border-default bg-default-100 text-warning",danger:"border-default bg-default-100 text-danger",foreground:"border-default bg-default-100 text-foreground"},light:{default:"bg-transparent text-default-foreground",primary:"bg-transparent text-primary",secondary:"bg-transparent text-secondary",success:"bg-transparent text-success",warning:"bg-transparent text-warning",danger:"bg-transparent text-danger",foreground:"bg-transparent text-foreground"},ghost:{default:"border-default text-default-foreground",primary:"border-primary text-primary",secondary:"border-secondary text-secondary",success:"border-success text-success",warning:"border-warning text-warning",danger:"border-danger text-danger",foreground:"border-foreground text-foreground hover:!bg-foreground"}}},1206:(e,t,r)=>{r.d(t,{tv:()=>et});var n=r(45628),o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=e=>!e||"object"!=typeof e||0===Object.keys(e).length,i=(e,t)=>JSON.stringify(e)===JSON.stringify(t);function s(e){let t=[];return function e(t,r){t.forEach(function(t){Array.isArray(t)?e(t,r):r.push(t)})}(e,t),t}var l=(...e)=>s(e).filter(Boolean),u=(e,t)=>{let r={},n=Object.keys(e),o=Object.keys(t);for(let a of n)if(o.includes(a)){let n=e[a],o=t[a];"object"==typeof n&&"object"==typeof o?r[a]=u(n,o):Array.isArray(n)||Array.isArray(o)?r[a]=l(o,n):r[a]=o+" "+n}else r[a]=e[a];for(let e of o)n.includes(e)||(r[e]=t[e]);return r},d=e=>e&&"string"==typeof e?e.replace(/\s+/g," ").trim():e,c=/^\[(.+)\]$/;function f(e,t){var r=e;return t.split("-").forEach(function(e){r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r}var p=/\s+/;function g(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=function e(t){if("string"==typeof t)return t;for(var r,n="",o=0;o<t.length;o++)t[o]&&(r=e(t[o]))&&(n&&(n+=" "),n+=r);return n}(e))&&(n&&(n+=" "),n+=t);return n}function m(){for(var e,t,r,n=arguments.length,o=Array(n),a=0;a<n;a++)o[a]=arguments[a];var i=function(n){var a=o[0];return t=(e=function(e){var t,r,n,o,a,i,s,l,u,d,p;return{cache:function(e){if(e<1)return{get:function(){},set:function(){}};var t=0,r=new Map,n=new Map;function o(o,a){r.set(o,a),++t>e&&(t=0,n=r,r=new Map)}return{get:function(e){var t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set:function(e,t){r.has(e)?r.set(e,t):o(e,t)}}}(e.cacheSize),splitModifiers:(r=1===(t=e.separator||":").length,n=t[0],o=t.length,function(e){for(var a,i=[],s=0,l=0,u=0;u<e.length;u++){var d=e[u];if(0===s){if(d===n&&(r||e.slice(u,u+o)===t)){i.push(e.slice(l,u)),l=u+o;continue}if("/"===d){a=u;continue}}"["===d?s++:"]"===d&&s--}var c=0===i.length?e:e.substring(l),f=c.startsWith("!"),p=f?c.substring(1):c;return{modifiers:i,hasImportantModifier:f,baseClassName:p,maybePostfixModifierPosition:a&&a>l?a-l:void 0}}),...(l=e.theme,u=e.prefix,d={nextPart:new Map,validators:[]},(p=Object.entries(e.classGroups),u?p.map(function(e){return[e[0],e[1].map(function(e){return"string"==typeof e?u+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(function(e){return[u+e[0],e[1]]})):e})]}):p).forEach(function(e){var t=e[0];(function e(t,r,n,o){t.forEach(function(t){if("string"==typeof t){(""===t?r:f(r,t)).classGroupId=n;return}if("function"==typeof t){if(t.isThemeGetter){e(t(o),r,n,o);return}r.validators.push({validator:t,classGroupId:n});return}Object.entries(t).forEach(function(t){var a=t[0];e(t[1],f(r,a),n,o)})})})(e[1],d,t,l)}),a=e.conflictingClassGroups,s=void 0===(i=e.conflictingClassGroupModifiers)?{}:i,{getClassGroupId:function(e){var t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),function e(t,r){if(0===t.length)return r.classGroupId;var n=t[0],o=r.nextPart.get(n),a=o?e(t.slice(1),o):void 0;if(a)return a;if(0!==r.validators.length){var i=t.join("-");return r.validators.find(function(e){return(0,e.validator)(i)})?.classGroupId}}(t,d)||function(e){if(c.test(e)){var t=c.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}}(e)},getConflictingClassGroupIds:function(e,t){var r=a[e]||[];return t&&s[e]?[].concat(r,s[e]):r}})}}(o.slice(1).reduce(function(e,t){return t(e)},a()))).cache.get,r=e.cache.set,i=s,s(n)};function s(n){var o,a,i,s,l,u=t(n);if(u)return u;var d=(a=(o=e).splitModifiers,i=o.getClassGroupId,s=o.getConflictingClassGroupIds,l=new Set,n.trim().split(p).map(function(e){var t=a(e),r=t.modifiers,n=t.hasImportantModifier,o=t.baseClassName,s=t.maybePostfixModifierPosition,l=i(s?o.substring(0,s):o),u=!!s;if(!l){if(!s||!(l=i(o)))return{isTailwindClass:!1,originalClassName:e};u=!1}var d=(function(e){if(e.length<=1)return e;var t=[],r=[];return e.forEach(function(e){"["===e[0]?(t.push.apply(t,r.sort().concat([e])),r=[]):r.push(e)}),t.push.apply(t,r.sort()),t})(r).join(":");return{isTailwindClass:!0,modifierId:n?d+"!":d,classGroupId:l,originalClassName:e,hasPostfixModifier:u}}).reverse().filter(function(e){if(!e.isTailwindClass)return!0;var t=e.modifierId,r=e.classGroupId,n=e.hasPostfixModifier,o=t+r;return!l.has(o)&&(l.add(o),s(r,n).forEach(function(e){return l.add(t+e)}),!0)}).reverse().map(function(e){return e.originalClassName}).join(" "));return r(n,d),d}return function(){return i(g.apply(null,arguments))}}function v(e){var t=function(t){return t[e]||[]};return t.isThemeGetter=!0,t}var b=/^\[(?:([a-z-]+):)?(.+)\]$/i,y=/^\d+\/\d+$/,h=new Set(["px","full","screen"]),w=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,x=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,T=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function E(e){return j(e)||h.has(e)||y.test(e)||k(e)}function k(e){return N(e,"length",$)}function P(e){return N(e,"size",F)}function C(e){return N(e,"position",F)}function S(e){return N(e,"url",D)}function M(e){return N(e,"number",j)}function j(e){return!Number.isNaN(Number(e))}function O(e){return e.endsWith("%")&&j(e.slice(0,-1))}function I(e){return _(e)||N(e,"number",_)}function L(e){return b.test(e)}function A(){return!0}function R(e){return w.test(e)}function z(e){return N(e,"",K)}function N(e,t,r){var n=b.exec(e);return!!n&&(n[1]?n[1]===t:r(n[2]))}function $(e){return x.test(e)}function F(){return!1}function D(e){return e.startsWith("url(")}function _(e){return Number.isInteger(Number(e))}function K(e){return T.test(e)}function W(){var e=v("colors"),t=v("spacing"),r=v("blur"),n=v("brightness"),o=v("borderColor"),a=v("borderRadius"),i=v("borderSpacing"),s=v("borderWidth"),l=v("contrast"),u=v("grayscale"),d=v("hueRotate"),c=v("invert"),f=v("gap"),p=v("gradientColorStops"),g=v("gradientColorStopPositions"),m=v("inset"),b=v("margin"),y=v("opacity"),h=v("padding"),w=v("saturate"),x=v("scale"),T=v("sepia"),N=v("skew"),$=v("space"),F=v("translate"),D=function(){return["auto","contain","none"]},_=function(){return["auto","hidden","clip","visible","scroll"]},K=function(){return["auto",L,t]},W=function(){return[L,t]},V=function(){return["",E]},G=function(){return["auto",j,L]},H=function(){return["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"]},B=function(){return["solid","dashed","dotted","double","none"]},U=function(){return["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},Y=function(){return["start","end","center","between","around","evenly","stretch"]},X=function(){return["","0",L]},q=function(){return["auto","avoid","all","avoid-page","page","left","right","column"]},J=function(){return[j,M]},Q=function(){return[j,L]};return{cacheSize:500,theme:{colors:[A],spacing:[E],blur:["none","",R,L],brightness:J(),borderColor:[e],borderRadius:["none","","full",R,L],borderSpacing:W(),borderWidth:V(),contrast:J(),grayscale:X(),hueRotate:Q(),invert:X(),gap:W(),gradientColorStops:[e],gradientColorStopPositions:[O,k],inset:K(),margin:K(),opacity:J(),padding:W(),saturate:J(),scale:J(),sepia:X(),skew:Q(),space:W(),translate:W()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[R]}],"break-after":[{"break-after":q()}],"break-before":[{"break-before":q()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(H(),[L])}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",I]}],basis:[{basis:K()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",I]}],"grid-cols":[{"grid-cols":[A]}],"col-start-end":[{col:["auto",{span:["full",I]},L]}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":[A]}],"row-start-end":[{row:["auto",{span:[I]},L]}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal"].concat(Y())}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(Y(),["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(Y(),["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[$]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[$]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",L,t]}],"min-w":[{"min-w":["min","max","fit",L,E]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[R]},R,L]}],h:[{h:[L,t,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",L,E]}],"max-h":[{"max-h":[L,t,"min","max","fit"]}],"font-size":[{text:["base",R,k]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",M]}],"font-family":[{font:[A]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",j,M]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",L,E]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(B(),["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",E]}],"underline-offset":[{"underline-offset":["auto",L,E]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:W()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(H(),[C])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",P]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},S]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[].concat(B(),["hidden"])}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:B()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:[""].concat(B())}],"outline-offset":[{"outline-offset":[L,E]}],"outline-w":[{outline:[E]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[E]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",R,z]}],"shadow-color":[{shadow:[A]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":U()}],"bg-blend":[{"bg-blend":U()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",R,L]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[c]}],saturate:[{saturate:[w]}],sepia:[{sepia:[T]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[I,L]}],"translate-x":[{"translate-x":[F]}],"translate-y":[{"translate-y":[F]}],"skew-x":[{"skew-x":[N]}],"skew-y":[{"skew-y":[N]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":W()}],"scroll-mx":[{"scroll-mx":W()}],"scroll-my":[{"scroll-my":W()}],"scroll-ms":[{"scroll-ms":W()}],"scroll-me":[{"scroll-me":W()}],"scroll-mt":[{"scroll-mt":W()}],"scroll-mr":[{"scroll-mr":W()}],"scroll-mb":[{"scroll-mb":W()}],"scroll-ml":[{"scroll-ml":W()}],"scroll-p":[{"scroll-p":W()}],"scroll-px":[{"scroll-px":W()}],"scroll-py":[{"scroll-py":W()}],"scroll-ps":[{"scroll-ps":W()}],"scroll-pe":[{"scroll-pe":W()}],"scroll-pt":[{"scroll-pt":W()}],"scroll-pr":[{"scroll-pr":W()}],"scroll-pb":[{"scroll-pb":W()}],"scroll-pl":[{"scroll-pl":W()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[E,M]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var V=m(W),G=Object.prototype.hasOwnProperty,H=new Set(["string","number","boolean"]),B={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},U=e=>e||void 0,Y=(...e)=>U(s(e).filter(Boolean).join(" ")),X=null,q={},J=!1,Q=(...e)=>t=>t.twMerge?((!X||J)&&(J=!1,X=a(q)?V:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return"function"==typeof e?m.apply(void 0,[W,e].concat(r)):m.apply(void 0,[function(){return function(e,t){for(var r in t)(function e(t,r,n){if(!G.call(t,r)||H.has(typeof n)||null===n){t[r]=n;return}if(Array.isArray(n)&&Array.isArray(t[r])){t[r]=t[r].concat(n);return}if("object"==typeof n&&"object"==typeof t[r]){if(null===t[r]){t[r]=n;return}for(var o in n)e(t[r],o,n[o])}})(e,r,t[r]);return e}(W(),e)}].concat(r))}(q)),U(X(Y(e)))):Y(e),Z=(e,t)=>{for(let r in t)e.hasOwnProperty(r)?e[r]=Y(e[r],t[r]):e[r]=t[r];return e},ee=(e,t)=>{let{extend:r=null,slots:n={},variants:s={},compoundVariants:c=[],compoundSlots:f=[],defaultVariants:p={}}=e,g={...B,...t},m=null!=r&&r.base?Y(r.base,null==e?void 0:e.base):null==e?void 0:e.base,v=null!=r&&r.variants&&!a(r.variants)?u(s,r.variants):s,b=null!=r&&r.defaultVariants&&!a(r.defaultVariants)?{...r.defaultVariants,...p}:p;a(g.twMergeConfig)||i(g.twMergeConfig,q)||(J=!0,q=g.twMergeConfig);let y=a(null==r?void 0:r.slots),h=a(n)?{}:{base:Y(null==e?void 0:e.base,y&&(null==r?void 0:r.base)),...n},w=y?h:Z({...null==r?void 0:r.slots},a(h)?{base:null==e?void 0:e.base}:h),x=e=>{if(a(v)&&a(n)&&y)return Q(m,null==e?void 0:e.class,null==e?void 0:e.className)(g);if(c&&!Array.isArray(c))throw TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof c}`);if(f&&!Array.isArray(f))throw TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof f}`);let t=(e,t,r=[],n)=>{let o=r;if("string"==typeof t)o=o.concat(d(t).split(" ").map(t=>`${e}:${t}`));else if(Array.isArray(t))o=o.concat(t.reduce((t,r)=>t.concat(`${e}:${r}`),[]));else if("object"==typeof t&&"string"==typeof n){for(let r in t)if(t.hasOwnProperty(r)&&r===n){let a=t[r];if(a&&"string"==typeof a){let t=d(a);o[n]?o[n]=o[n].concat(t.split(" ").map(t=>`${e}:${t}`)):o[n]=t.split(" ").map(t=>`${e}:${t}`)}else Array.isArray(a)&&a.length>0&&(o[n]=a.reduce((t,r)=>t.concat(`${e}:${r}`),[]))}}return o},i=(r,n=v,i=null,s=null)=>{var l;let u=n[r];if(!u||a(u))return null;let d=null!=(l=null==s?void 0:s[r])?l:null==e?void 0:e[r];if(null===d)return null;let c=o(d),f=Array.isArray(g.responsiveVariants)&&g.responsiveVariants.length>0||!0===g.responsiveVariants,p=null==b?void 0:b[r],m=[];if("object"==typeof c&&f)for(let[e,r]of Object.entries(c)){let n=u[r];if("initial"===e){p=r;continue}Array.isArray(g.responsiveVariants)&&!g.responsiveVariants.includes(e)||(m=t(e,n,m,i))}let y=u[c]||u[o(p)];return"object"==typeof m&&"string"==typeof i&&m[i]?Z(m,y):m.length>0?(m.push(y),m):y},s=(e,t)=>{if(!v||"object"!=typeof v)return null;let r=[];for(let n in v){let o=i(n,v,e,t),a="base"===e&&"string"==typeof o?o:o&&o[e];a&&(r[r.length]=a)}return r},u={};for(let t in e)void 0!==e[t]&&(u[t]=e[t]);let p=(t,r)=>{var n;let o="object"==typeof(null==e?void 0:e[t])?{[t]:null==(n=e[t])?void 0:n.initial}:{};return{...b,...u,...o,...r}},h=(e=[],t)=>{let r=[];for(let{class:n,className:o,...a}of e){let e=!0;for(let[r,n]of Object.entries(a)){let o=p(r,t);if(Array.isArray(n)){if(!n.includes(o[r])){e=!1;break}}else if(o[r]!==n){e=!1;break}}e&&(n&&r.push(n),o&&r.push(o))}return r},x=e=>{let t=h(c,e);return l(h(null==r?void 0:r.compoundVariants,e),t)},T=e=>{let t=x(e);if(!Array.isArray(t))return t;let r={};for(let e of t)if("string"==typeof e&&(r.base=Q(r.base,e)(g)),"object"==typeof e)for(let[t,n]of Object.entries(e))r[t]=Q(r[t],n)(g);return r},E=e=>{if(f.length<1)return null;let t={};for(let{slots:r=[],class:n,className:o,...i}of f){if(!a(i)){let t=!0;for(let r of Object.keys(i)){let n=p(r,e)[r];if(void 0===n||(Array.isArray(i[r])?!i[r].includes(n):i[r]!==n)){t=!1;break}}if(!t)continue}for(let e of r)t[e]=t[e]||[],t[e].push([n,o])}return t};if(!a(n)||!y){let e={};if("object"==typeof w&&!a(w))for(let t of Object.keys(w))e[t]=e=>{var r,n;return Q(w[t],s(t,e),(null!=(r=T(e))?r:[])[t],(null!=(n=E(e))?n:[])[t],null==e?void 0:e.class,null==e?void 0:e.className)(g)};return e}return Q(m,v?Object.keys(v).map(e=>i(e,v)):null,x(),null==e?void 0:e.class,null==e?void 0:e.className)(g)};return x.variantKeys=(()=>{if(!(!v||"object"!=typeof v))return Object.keys(v)})(),x.extend=r,x.base=m,x.slots=w,x.variants=v,x.defaultVariants=b,x.compoundSlots=f,x.compoundVariants=c,x},et=(e,t)=>{var r,o,a;return ee(e,{...t,twMerge:null==(r=null==t?void 0:t.twMerge)||r,twMergeConfig:{...null==t?void 0:t.twMergeConfig,theme:{...null==(o=null==t?void 0:t.twMergeConfig)?void 0:o.theme,...n.w.theme},classGroups:{...null==(a=null==t?void 0:t.twMergeConfig)?void 0:a.classGroups,...n.w.classGroups}}})}},35130:(e,t,r)=>{r.d(t,{l:()=>u});var n=r(70427),o=r(3985),a=r(97262),i=r(53220),s=r(14463),l=r(63624);function u(e,t){let r,{elementType:u="button",isDisabled:d,onPress:c,onPressStart:f,onPressEnd:p,onPressChange:g,preventFocusOnPress:m,allowFocusWhenDisabled:v,onClick:b,href:y,target:h,rel:w,type:x="button",allowTextSelectionOnPress:T}=e;r="button"===u?{type:x,disabled:d}:{role:"button",tabIndex:d?void 0:0,href:"a"!==u||d?void 0:y,target:"a"===u?h:void 0,type:"input"===u?x:void 0,disabled:"input"===u?d:void 0,"aria-disabled":d&&"input"!==u?d:void 0,rel:"a"===u?w:void 0};let E=(0,o.un)()||(0,o.m0)();b&&"function"==typeof b&&(0,n.R)("onClick is deprecated, please use onPress instead. See: https://github.com/nextui-org/nextui/issues/4292","useButton");let{pressProps:k,isPressed:P}=(0,l.d)({onPressStart:f,onPressEnd:p,onPressChange:g,onPress:e=>{E&&(null==b||b(e)),null==c||c(e)},isDisabled:d,preventFocusOnPress:m,allowTextSelectionOnPress:T,ref:t}),{focusableProps:C}=(0,s.W)(e,t);v&&(C.tabIndex=d?-1:C.tabIndex);let S=(0,a.v)(C,k,(0,i.$)(e,{labelable:!0}));return{isPressed:P,buttonProps:(0,a.v)(r,S,{"aria-haspopup":e["aria-haspopup"],"aria-expanded":e["aria-expanded"],"aria-controls":e["aria-controls"],"aria-pressed":e["aria-pressed"],onClick:e=>{"button"===x&&E||null==b||b(e)}})}}},44193:(e,t,r)=>{r.d(t,{l:()=>s});var n=r(80243),o=r(67118),a=r(57651),i=r(1545);function s(e){let t=(0,n.T)(e);if("virtual"===(0,i.ME)()){let r=t.activeElement;(0,o.v)(()=>{t.activeElement===r&&e.isConnected&&(0,a.e)(e)})}else(0,a.e)(e)}},57010:(e,t,r)=>{r.d(t,{o:()=>s});var n=r(1545),o=r(98327),a=r(22258),i=r(12115);function s(e={}){let{autoFocus:t=!1,isTextInput:r,within:l}=e,u=(0,i.useRef)({isFocused:!1,isFocusVisible:t||(0,n.pP)()}),[d,c]=(0,i.useState)(!1),[f,p]=(0,i.useState)(()=>u.current.isFocused&&u.current.isFocusVisible),g=(0,i.useCallback)(()=>p(u.current.isFocused&&u.current.isFocusVisible),[]),m=(0,i.useCallback)(e=>{u.current.isFocused=e,c(e),g()},[g]);(0,n.K7)(e=>{u.current.isFocusVisible=e,g()},[],{isTextInput:r});let{focusProps:v}=(0,o.i)({isDisabled:l,onFocusChange:m}),{focusWithinProps:b}=(0,a.R)({isDisabled:!l,onFocusWithinChange:m});return{isFocused:d,isFocusVisible:f,focusProps:l?b:v}}},14463:(e,t,r)=>{r.d(t,{W:()=>d});var n=r(44193),o=r(54807),a=r(97262),i=r(12115),s=r(98327);function l(e){if(!e)return;let t=!0;return r=>{e({...r,preventDefault(){r.preventDefault()},isDefaultPrevented:()=>r.isDefaultPrevented(),stopPropagation(){console.error("stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior.")},continuePropagation(){t=!1}}),t&&r.stopPropagation()}}let u=i.createContext(null);function d(e,t){let{focusProps:r}=(0,s.i)(e),{keyboardProps:d}={keyboardProps:e.isDisabled?{}:{onKeyDown:l(e.onKeyDown),onKeyUp:l(e.onKeyUp)}},c=(0,a.v)(r,d),f=function(e){let t=(0,i.useContext)(u)||{};(0,o.w)(t,e);let{ref:r,...n}=t;return n}(t),p=e.isDisabled?{}:f,g=(0,i.useRef)(e.autoFocus);return(0,i.useEffect)(()=>{g.current&&t.current&&(0,n.l)(t.current),g.current=!1},[t]),{focusableProps:(0,a.v)({...c,tabIndex:e.excludeFromTabOrder&&!e.isDisabled?-1:void 0},p)}}},22167:(e,t,r)=>{r.d(t,{F:()=>n});let n=r(12115).createContext({register:()=>{}});n.displayName="PressResponderContext"},98327:(e,t,r)=>{r.d(t,{i:()=>i});var n=r(53721),o=r(12115),a=r(80243);function i(e){let{isDisabled:t,onFocus:r,onBlur:i,onFocusChange:s}=e,l=(0,o.useCallback)(e=>{if(e.target===e.currentTarget)return i&&i(e),s&&s(!1),!0},[i,s]),u=(0,n.y)(l),d=(0,o.useCallback)(e=>{let t=(0,a.T)(e.target);e.target===e.currentTarget&&t.activeElement===e.target&&(r&&r(e),s&&s(!0),u(e))},[s,r,u]);return{focusProps:{onFocus:!t&&(r||s||i)?d:void 0,onBlur:!t&&(i||s)?l:void 0}}}},1545:(e,t,r)=>{r.d(t,{Cl:()=>E,K7:()=>P,ME:()=>T,pP:()=>x});var n=r(3985),o=r(80283),a=r(80243),i=r(12115);let s=null,l=new Set,u=new Map,d=!1,c=!1,f={Tab:!0,Escape:!0};function p(e,t){for(let r of l)r(e,t)}function g(e){d=!0,e.metaKey||!(0,n.cX)()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(s="keyboard",p("keyboard",e))}function m(e){s="pointer",("mousedown"===e.type||"pointerdown"===e.type)&&(d=!0,p("pointer",e))}function v(e){(0,o.Y)(e)&&(d=!0,s="virtual")}function b(e){e.target!==window&&e.target!==document&&(d||c||(s="virtual",p("virtual",e)),d=!1,c=!1)}function y(){d=!1,c=!0}function h(e){if("undefined"==typeof window||u.get((0,a.m)(e)))return;let t=(0,a.m)(e),r=(0,a.T)(e),n=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){d=!0,n.apply(this,arguments)},r.addEventListener("keydown",g,!0),r.addEventListener("keyup",g,!0),r.addEventListener("click",v,!0),t.addEventListener("focus",b,!0),t.addEventListener("blur",y,!1),"undefined"!=typeof PointerEvent?(r.addEventListener("pointerdown",m,!0),r.addEventListener("pointermove",m,!0),r.addEventListener("pointerup",m,!0)):(r.addEventListener("mousedown",m,!0),r.addEventListener("mousemove",m,!0),r.addEventListener("mouseup",m,!0)),t.addEventListener("beforeunload",()=>{w(e)},{once:!0}),u.set(t,{focus:n})}let w=(e,t)=>{let r=(0,a.m)(e),n=(0,a.T)(e);t&&n.removeEventListener("DOMContentLoaded",t),u.has(r)&&(r.HTMLElement.prototype.focus=u.get(r).focus,n.removeEventListener("keydown",g,!0),n.removeEventListener("keyup",g,!0),n.removeEventListener("click",v,!0),r.removeEventListener("focus",b,!0),r.removeEventListener("blur",y,!1),"undefined"!=typeof PointerEvent?(n.removeEventListener("pointerdown",m,!0),n.removeEventListener("pointermove",m,!0),n.removeEventListener("pointerup",m,!0)):(n.removeEventListener("mousedown",m,!0),n.removeEventListener("mousemove",m,!0),n.removeEventListener("mouseup",m,!0)),u.delete(r))};function x(){return"pointer"!==s}function T(){return s}function E(e){s=e,p(e,null)}"undefined"!=typeof document&&function(e){let t;let r=(0,a.T)(void 0);"loading"!==r.readyState?h(void 0):(t=()=>{h(void 0)},r.addEventListener("DOMContentLoaded",t)),()=>w(e,t)}();let k=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function P(e,t,r){h(),(0,i.useEffect)(()=>{let t=(t,n)=>{(function(e,t,r){var n;let o="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).HTMLInputElement:HTMLInputElement,i="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,s="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).HTMLElement:HTMLElement,l="undefined"!=typeof window?(0,a.m)(null==r?void 0:r.target).KeyboardEvent:KeyboardEvent;return!((e=e||(null==r?void 0:r.target)instanceof o&&!k.has(null==r?void 0:null===(n=r.target)||void 0===n?void 0:n.type)||(null==r?void 0:r.target)instanceof i||(null==r?void 0:r.target)instanceof s&&(null==r?void 0:r.target.isContentEditable))&&"keyboard"===t&&r instanceof l&&!f[r.key])})(!!(null==r?void 0:r.isTextInput),t,n)&&e(x())};return l.add(t),()=>{l.delete(t)}},t)}},22258:(e,t,r)=>{r.d(t,{R:()=>a});var n=r(53721),o=r(12115);function a(e){let{isDisabled:t,onBlurWithin:r,onFocusWithin:a,onFocusWithinChange:i}=e,s=(0,o.useRef)({isFocusWithin:!1}),l=(0,o.useCallback)(e=>{s.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(s.current.isFocusWithin=!1,r&&r(e),i&&i(!1))},[r,i,s]),u=(0,n.y)(l),d=(0,o.useCallback)(e=>{s.current.isFocusWithin||document.activeElement!==e.target||(a&&a(e),i&&i(!0),s.current.isFocusWithin=!0,u(e))},[a,i,u]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:d,onBlur:l}}}},13201:(e,t,r)=>{r.d(t,{M:()=>u});var n=r(12115);let o=!1,a=0;function i(){o=!0,setTimeout(()=>{o=!1},50)}function s(e){"touch"===e.pointerType&&i()}function l(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent?document.addEventListener("pointerup",s):document.addEventListener("touchend",i),a++,()=>{--a>0||("undefined"!=typeof PointerEvent?document.removeEventListener("pointerup",s):document.removeEventListener("touchend",i))}}function u(e){let{onHoverStart:t,onHoverChange:r,onHoverEnd:a,isDisabled:i}=e,[s,u]=(0,n.useState)(!1),d=(0,n.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,n.useEffect)(l,[]);let{hoverProps:c,triggerHoverEnd:f}=(0,n.useMemo)(()=>{let e=(e,n)=>{if(d.pointerType=n,i||"touch"===n||d.isHovered||!e.currentTarget.contains(e.target))return;d.isHovered=!0;let o=e.currentTarget;d.target=o,t&&t({type:"hoverstart",target:o,pointerType:n}),r&&r(!0),u(!0)},n=(e,t)=>{if(d.pointerType="",d.target=null,"touch"===t||!d.isHovered)return;d.isHovered=!1;let n=e.currentTarget;a&&a({type:"hoverend",target:n,pointerType:t}),r&&r(!1),u(!1)},s={};return"undefined"!=typeof PointerEvent?(s.onPointerEnter=t=>{o&&"mouse"===t.pointerType||e(t,t.pointerType)},s.onPointerLeave=e=>{!i&&e.currentTarget.contains(e.target)&&n(e,e.pointerType)}):(s.onTouchStart=()=>{d.ignoreEmulatedMouseEvents=!0},s.onMouseEnter=t=>{d.ignoreEmulatedMouseEvents||o||e(t,"mouse"),d.ignoreEmulatedMouseEvents=!1},s.onMouseLeave=e=>{!i&&e.currentTarget.contains(e.target)&&n(e,"mouse")}),{hoverProps:s,triggerHoverEnd:n}},[t,r,a,i,d]);return(0,n.useEffect)(()=>{i&&f({currentTarget:d.target},d.pointerType)},[i]),{hoverProps:c,isHovered:s}}},63624:(e,t,r)=>{r.d(t,{d:()=>C});var n=r(3985),o=r(80243),a=r(67118);let i="default",s="",l=new WeakMap;function u(e){if((0,n.un)()){if("default"===i){let t=(0,o.T)(e);s=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}i="disabled"}else(e instanceof HTMLElement||e instanceof SVGElement)&&(l.set(e,e.style.userSelect),e.style.userSelect="none")}function d(e){if((0,n.un)())"disabled"===i&&(i="restoring",setTimeout(()=>{(0,a.v)(()=>{if("restoring"===i){let t=(0,o.T)(e);"none"===t.documentElement.style.webkitUserSelect&&(t.documentElement.style.webkitUserSelect=s||""),s="",i="default"}})},300));else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&l.has(e)){let t=l.get(e);"none"===e.style.userSelect&&(e.style.userSelect=t),""===e.getAttribute("style")&&e.removeAttribute("style"),l.delete(e)}}var c=r(22167);function f(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function p(e,t,r){var n=f(e,t,"set");return!function(e,t,r){if(t.set)t.set.call(e,r);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=r}}(e,n,r),r}var g=r(97262),m=r(54807),v=r(45839),b=r(52378),y=r(67093),h=r(90872),w=r(80283),x=r(57651),T=r(12115),E=new WeakMap;class k{continuePropagation(){p(this,E,!1)}get shouldStopPropagation(){var e;return(e=f(this,E,"get")).get?e.get.call(this):e.value}constructor(e,t,r,n){var o;!function(e,t,r){(function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,r)}(this,E,{writable:!0,value:void 0}),p(this,E,!0);let a=null!==(o=null==n?void 0:n.target)&&void 0!==o?o:r.currentTarget,i=null==a?void 0:a.getBoundingClientRect(),s,l=0,u,d=null;null!=r.clientX&&null!=r.clientY&&(u=r.clientX,d=r.clientY),i&&(null!=u&&null!=d?(s=u-i.left,l=d-i.top):(s=i.width/2,l=i.height/2)),this.type=e,this.pointerType=t,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=l}}let P=Symbol("linkClicked");function C(e){let{onPress:t,onPressChange:r,onPressStart:a,onPressEnd:i,onPressUp:s,isDisabled:l,isPressed:f,preventFocusOnPress:p,shouldCancelOnPointerExit:E,allowTextSelectionOnPress:C,ref:N,...$}=function(e){let t=(0,T.useContext)(c.F);if(t){let{register:r,...n}=t;e=(0,g.v)(n,e),r()}return(0,m.w)(t,e.ref),e}(e),[F,D]=(0,T.useState)(!1),_=(0,T.useRef)({isPressed:!1,ignoreEmulatedMouseEvents:!1,ignoreClickAfterPress:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null}),{addGlobalListener:K,removeAllGlobalListeners:W}=(0,v.A)(),V=(0,b.J)((e,t)=>{let n=_.current;if(l||n.didFirePressStart)return!1;let o=!0;if(n.isTriggeringEvent=!0,a){let r=new k("pressstart",t,e);a(r),o=r.shouldStopPropagation}return r&&r(!0),n.isTriggeringEvent=!1,n.didFirePressStart=!0,D(!0),o}),G=(0,b.J)((e,n,o=!0)=>{let a=_.current;if(!a.didFirePressStart)return!1;a.ignoreClickAfterPress=!0,a.didFirePressStart=!1,a.isTriggeringEvent=!0;let s=!0;if(i){let t=new k("pressend",n,e);i(t),s=t.shouldStopPropagation}if(r&&r(!1),D(!1),t&&o&&!l){let r=new k("press",n,e);t(r),s&&(s=r.shouldStopPropagation)}return a.isTriggeringEvent=!1,s}),H=(0,b.J)((e,t)=>{let r=_.current;if(l)return!1;if(s){r.isTriggeringEvent=!0;let n=new k("pressup",t,e);return s(n),r.isTriggeringEvent=!1,n.shouldStopPropagation}return!0}),B=(0,b.J)(e=>{let t=_.current;t.isPressed&&t.target&&(t.isOverTarget&&null!=t.pointerType&&G(I(t.target,e),t.pointerType,!1),t.isPressed=!1,t.isOverTarget=!1,t.activePointerId=null,t.pointerType=null,W(),C||d(t.target))}),U=(0,b.J)(e=>{E&&B(e)}),Y=(0,T.useMemo)(()=>{let e=_.current,t={onKeyDown(t){if(M(t.nativeEvent,t.currentTarget)&&t.currentTarget.contains(t.target)){var a;z(t.target,t.key)&&t.preventDefault();let i=!0;if(!e.isPressed&&!t.repeat){e.target=t.currentTarget,e.isPressed=!0,i=V(t,"keyboard");let n=t.currentTarget;K((0,o.T)(t.currentTarget),"keyup",(0,y.c)(t=>{M(t,n)&&!t.repeat&&n.contains(t.target)&&e.target&&H(I(e.target,t),"keyboard")},r),!0)}i&&t.stopPropagation(),t.metaKey&&(0,n.cX)()&&(null===(a=e.metaKeyEvents)||void 0===a||a.set(t.key,t.nativeEvent))}else"Meta"===t.key&&(e.metaKeyEvents=new Map)},onClick(t){if((!t||t.currentTarget.contains(t.target))&&t&&0===t.button&&!e.isTriggeringEvent&&!h.Fe.isOpening){let r=!0;if(l&&t.preventDefault(),!e.ignoreClickAfterPress&&!e.ignoreEmulatedMouseEvents&&!e.isPressed&&("virtual"===e.pointerType||(0,w.Y)(t.nativeEvent))){l||p||(0,x.e)(t.currentTarget);let e=V(t,"virtual"),n=H(t,"virtual"),o=G(t,"virtual");r=e&&n&&o}e.ignoreEmulatedMouseEvents=!1,e.ignoreClickAfterPress=!1,r&&t.stopPropagation()}}},r=t=>{var r,n,o;if(e.isPressed&&e.target&&M(t,e.target)){z(t.target,t.key)&&t.preventDefault();let r=t.target;G(I(e.target,t),"keyboard",e.target.contains(r)),W(),"Enter"!==t.key&&S(e.target)&&e.target.contains(r)&&!t[P]&&(t[P]=!0,(0,h.Fe)(e.target,t,!1)),e.isPressed=!1,null===(n=e.metaKeyEvents)||void 0===n||n.delete(t.key)}else if("Meta"===t.key&&(null===(r=e.metaKeyEvents)||void 0===r?void 0:r.size)){let t=e.metaKeyEvents;for(let r of(e.metaKeyEvents=void 0,t.values()))null===(o=e.target)||void 0===o||o.dispatchEvent(new KeyboardEvent("keyup",r))}};if("undefined"!=typeof PointerEvent){t.onPointerDown=t=>{if(0!==t.button||!t.currentTarget.contains(t.target))return;if((0,w.P)(t.nativeEvent)){e.pointerType="virtual";return}A(t.currentTarget)&&t.preventDefault(),e.pointerType=t.pointerType;let a=!0;e.isPressed||(e.isPressed=!0,e.isOverTarget=!0,e.activePointerId=t.pointerId,e.target=t.currentTarget,l||p||(0,x.e)(t.currentTarget),C||u(e.target),a=V(t,e.pointerType),K((0,o.T)(t.currentTarget),"pointermove",r,!1),K((0,o.T)(t.currentTarget),"pointerup",n,!1),K((0,o.T)(t.currentTarget),"pointercancel",i,!1)),a&&t.stopPropagation()},t.onMouseDown=e=>{e.currentTarget.contains(e.target)&&0===e.button&&(A(e.currentTarget)&&e.preventDefault(),e.stopPropagation())},t.onPointerUp=t=>{t.currentTarget.contains(t.target)&&"virtual"!==e.pointerType&&0===t.button&&L(t,t.currentTarget)&&H(t,e.pointerType||t.pointerType)};let r=t=>{t.pointerId===e.activePointerId&&(e.target&&L(t,e.target)?e.isOverTarget||null==e.pointerType||(e.isOverTarget=!0,V(I(e.target,t),e.pointerType)):e.target&&e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!1,G(I(e.target,t),e.pointerType,!1),U(t)))},n=t=>{t.pointerId===e.activePointerId&&e.isPressed&&0===t.button&&e.target&&(L(t,e.target)&&null!=e.pointerType?G(I(e.target,t),e.pointerType):e.isOverTarget&&null!=e.pointerType&&G(I(e.target,t),e.pointerType,!1),e.isPressed=!1,e.isOverTarget=!1,e.activePointerId=null,e.pointerType=null,W(),C||d(e.target),"ontouchend"in e.target&&"mouse"!==t.pointerType&&K(e.target,"touchend",a,{once:!0}))},a=e=>{R(e.currentTarget)&&e.preventDefault()},i=e=>{B(e)};t.onDragStart=e=>{e.currentTarget.contains(e.target)&&B(e)}}else{t.onMouseDown=t=>{if(0===t.button&&t.currentTarget.contains(t.target)){if(A(t.currentTarget)&&t.preventDefault(),e.ignoreEmulatedMouseEvents){t.stopPropagation();return}e.isPressed=!0,e.isOverTarget=!0,e.target=t.currentTarget,e.pointerType=(0,w.Y)(t.nativeEvent)?"virtual":"mouse",l||p||(0,x.e)(t.currentTarget),V(t,e.pointerType)&&t.stopPropagation(),K((0,o.T)(t.currentTarget),"mouseup",r,!1)}},t.onMouseEnter=t=>{if(!t.currentTarget.contains(t.target))return;let r=!0;e.isPressed&&!e.ignoreEmulatedMouseEvents&&null!=e.pointerType&&(e.isOverTarget=!0,r=V(t,e.pointerType)),r&&t.stopPropagation()},t.onMouseLeave=t=>{if(!t.currentTarget.contains(t.target))return;let r=!0;e.isPressed&&!e.ignoreEmulatedMouseEvents&&null!=e.pointerType&&(e.isOverTarget=!1,r=G(t,e.pointerType,!1),U(t)),r&&t.stopPropagation()},t.onMouseUp=t=>{t.currentTarget.contains(t.target)&&!e.ignoreEmulatedMouseEvents&&0===t.button&&H(t,e.pointerType||"mouse")};let r=t=>{if(0===t.button){if(e.isPressed=!1,W(),e.ignoreEmulatedMouseEvents){e.ignoreEmulatedMouseEvents=!1;return}e.target&&L(t,e.target)&&null!=e.pointerType?G(I(e.target,t),e.pointerType):e.target&&e.isOverTarget&&null!=e.pointerType&&G(I(e.target,t),e.pointerType,!1),e.isOverTarget=!1}};t.onTouchStart=t=>{if(!t.currentTarget.contains(t.target))return;let r=function(e){let{targetTouches:t}=e;return t.length>0?t[0]:null}(t.nativeEvent);r&&(e.activePointerId=r.identifier,e.ignoreEmulatedMouseEvents=!0,e.isOverTarget=!0,e.isPressed=!0,e.target=t.currentTarget,e.pointerType="touch",l||p||(0,x.e)(t.currentTarget),C||u(e.target),V(O(e.target,t),e.pointerType)&&t.stopPropagation(),K((0,o.m)(t.currentTarget),"scroll",n,!0))},t.onTouchMove=t=>{if(!t.currentTarget.contains(t.target))return;if(!e.isPressed){t.stopPropagation();return}let r=j(t.nativeEvent,e.activePointerId),n=!0;r&&L(r,t.currentTarget)?e.isOverTarget||null==e.pointerType||(e.isOverTarget=!0,n=V(O(e.target,t),e.pointerType)):e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!1,n=G(O(e.target,t),e.pointerType,!1),U(O(e.target,t))),n&&t.stopPropagation()},t.onTouchEnd=t=>{if(!t.currentTarget.contains(t.target))return;if(!e.isPressed){t.stopPropagation();return}let r=j(t.nativeEvent,e.activePointerId),n=!0;r&&L(r,t.currentTarget)&&null!=e.pointerType?(H(O(e.target,t),e.pointerType),n=G(O(e.target,t),e.pointerType)):e.isOverTarget&&null!=e.pointerType&&(n=G(O(e.target,t),e.pointerType,!1)),n&&t.stopPropagation(),e.isPressed=!1,e.activePointerId=null,e.isOverTarget=!1,e.ignoreEmulatedMouseEvents=!0,e.target&&!C&&d(e.target),W()},t.onTouchCancel=t=>{t.currentTarget.contains(t.target)&&(t.stopPropagation(),e.isPressed&&B(O(e.target,t)))};let n=t=>{e.isPressed&&t.target.contains(e.target)&&B({currentTarget:e.target,shiftKey:!1,ctrlKey:!1,metaKey:!1,altKey:!1})};t.onDragStart=e=>{e.currentTarget.contains(e.target)&&B(e)}}return t},[K,l,p,W,C,B,U,G,V,H]);return(0,T.useEffect)(()=>()=>{var e;C||d(null!==(e=_.current.target)&&void 0!==e?e:void 0)},[C]),{isPressed:f||F,pressProps:(0,g.v)($,Y)}}function S(e){return"A"===e.tagName&&e.hasAttribute("href")}function M(e,t){let{key:r,code:n}=e,a=t.getAttribute("role");return("Enter"===r||" "===r||"Spacebar"===r||"Space"===n)&&!(t instanceof(0,o.m)(t).HTMLInputElement&&!$(t,r)||t instanceof(0,o.m)(t).HTMLTextAreaElement||t.isContentEditable)&&!(("link"===a||!a&&S(t))&&"Enter"!==r)}function j(e,t){let r=e.changedTouches;for(let e=0;e<r.length;e++){let n=r[e];if(n.identifier===t)return n}return null}function O(e,t){let r=0,n=0;return t.targetTouches&&1===t.targetTouches.length&&(r=t.targetTouches[0].clientX,n=t.targetTouches[0].clientY),{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:r,clientY:n}}function I(e,t){let r=t.clientX,n=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:r,clientY:n}}function L(e,t){let r,n,o=t.getBoundingClientRect(),a=(r=0,n=0,void 0!==e.width?r=e.width/2:void 0!==e.radiusX&&(r=e.radiusX),void 0!==e.height?n=e.height/2:void 0!==e.radiusY&&(n=e.radiusY),{top:e.clientY-n,right:e.clientX+r,bottom:e.clientY+n,left:e.clientX-r});return!(o.left>a.right)&&!(a.left>o.right)&&!(o.top>a.bottom)&&!(a.top>o.bottom)}function A(e){return!(e instanceof HTMLElement)||!e.hasAttribute("draggable")}function R(e){return!(e instanceof HTMLInputElement)&&(e instanceof HTMLButtonElement?"submit"!==e.type&&"reset"!==e.type:!S(e))}function z(e,t){return e instanceof HTMLInputElement?!$(e,t):R(e)}let N=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function $(e,t){return"checkbox"===e.type||"radio"===e.type?" "===t:N.has(e.type)}},53721:(e,t,r)=>{r.d(t,{y:()=>s});var n=r(12115),o=r(8858),a=r(52378);class i{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(e,t){this.nativeEvent=t,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.bubbles=t.bubbles,this.cancelable=t.cancelable,this.defaultPrevented=t.defaultPrevented,this.eventPhase=t.eventPhase,this.isTrusted=t.isTrusted,this.timeStamp=t.timeStamp,this.type=e}}function s(e){let t=(0,n.useRef)({isFocused:!1,observer:null});(0,o.N)(()=>{let e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let r=(0,a.J)(t=>{null==e||e(t)});return(0,n.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let n=e.target;n.addEventListener("focusout",e=>{t.current.isFocused=!1,n.disabled&&r(new i("blur",e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)},{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&n.disabled){var e;null===(e=t.current.observer)||void 0===e||e.disconnect();let r=n===document.activeElement?null:document.activeElement;n.dispatchEvent(new FocusEvent("blur",{relatedTarget:r})),n.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:r}))}}),t.current.observer.observe(n,{attributes:!0,attributeFilter:["disabled"]})}},[r])}},67093:(e,t,r)=>{r.d(t,{c:()=>n});function n(...e){return(...t)=>{for(let r of e)"function"==typeof r&&r(...t)}}},80243:(e,t,r)=>{r.d(t,{T:()=>n,m:()=>o});let n=e=>{var t;return null!==(t=null==e?void 0:e.ownerDocument)&&void 0!==t?t:document},o=e=>e&&"window"in e&&e.window===e?e:n(e).defaultView||window},53220:(e,t,r)=>{r.d(t,{$:()=>s});let n=new Set(["id"]),o=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),a=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),i=/^(data-.*)$/;function s(e,t={}){let{labelable:r,isLink:l,propNames:u}=t,d={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&o.has(t)||l&&a.has(t)||(null==u?void 0:u.has(t))||i.test(t))&&(d[t]=e[t]);return d}},80283:(e,t,r)=>{r.d(t,{P:()=>a,Y:()=>o});var n=r(3985);function o(e){return 0===e.mozInputSource&&!!e.isTrusted||((0,n.m0)()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function a(e){return!(0,n.m0)()&&0===e.width&&0===e.height||1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType}},97262:(e,t,r)=>{r.d(t,{v:()=>i});var n=r(67093),o=r(84763),a=r(43463);function i(...e){let t={...e[0]};for(let r=1;r<e.length;r++){let i=e[r];for(let e in i){let r=t[e],s=i[e];"function"==typeof r&&"function"==typeof s&&"o"===e[0]&&"n"===e[1]&&e.charCodeAt(2)>=65&&90>=e.charCodeAt(2)?t[e]=(0,n.c)(r,s):("className"===e||"UNSAFE_className"===e)&&"string"==typeof r&&"string"==typeof s?t[e]=(0,a.A)(r,s):"id"===e&&r&&s?t.id=(0,o.Tw)(r,s):t[e]=void 0!==s?s:r}}return t}},67118:(e,t,r)=>{r.d(t,{v:()=>i});let n=new Map,o=new Set;function a(){if("undefined"==typeof window)return;function e(e){return"propertyName"in e}let t=r=>{if(!e(r)||!r.target)return;let a=n.get(r.target);if(a&&(a.delete(r.propertyName),0===a.size&&(r.target.removeEventListener("transitioncancel",t),n.delete(r.target)),0===n.size)){for(let e of o)e();o.clear()}};document.body.addEventListener("transitionrun",r=>{if(!e(r)||!r.target)return;let o=n.get(r.target);o||(o=new Set,n.set(r.target,o),r.target.addEventListener("transitioncancel",t,{once:!0})),o.add(r.propertyName)}),document.body.addEventListener("transitionend",t)}function i(e){requestAnimationFrame(()=>{0===n.size?e():o.add(e)})}"undefined"!=typeof document&&("loading"!==document.readyState?a():document.addEventListener("DOMContentLoaded",a))},52378:(e,t,r)=>{r.d(t,{J:()=>a});var n=r(8858),o=r(12115);function a(e){let t=(0,o.useRef)(null);return(0,n.N)(()=>{t.current=e},[e]),(0,o.useCallback)((...e)=>{let r=t.current;return null==r?void 0:r(...e)},[])}},45839:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(12115);function o(){let e=(0,n.useRef)(new Map),t=(0,n.useCallback)((t,r,n,o)=>{let a=(null==o?void 0:o.once)?(...t)=>{e.current.delete(n),n(...t)}:n;e.current.set(n,{type:r,eventTarget:t,fn:a,options:o}),t.addEventListener(r,n,o)},[]),r=(0,n.useCallback)((t,r,n,o)=>{var a;let i=(null===(a=e.current.get(n))||void 0===a?void 0:a.fn)||n;t.removeEventListener(r,i,o),e.current.delete(n)},[]),o=(0,n.useCallback)(()=>{e.current.forEach((e,t)=>{r(e.eventTarget,e.type,t,e.options)})},[r]);return(0,n.useEffect)(()=>o,[o]),{addGlobalListener:t,removeGlobalListener:r,removeAllGlobalListeners:o}}},84763:(e,t,r)=>{r.d(t,{Tw:()=>d,Bi:()=>u,X1:()=>c});var n=r(8858),o=r(52378),a=r(12115),i=r(71632);let s=!!("undefined"!=typeof window&&window.document&&window.document.createElement),l=new Map;function u(e){let[t,r]=(0,a.useState)(e),o=(0,a.useRef)(null),u=(0,i.Cc)(t),d=(0,a.useCallback)(e=>{o.current=e},[]);return s&&(l.has(u)&&!l.get(u).includes(d)?l.set(u,[...l.get(u),d]):l.set(u,[d])),(0,n.N)(()=>()=>{l.delete(u)},[u]),(0,a.useEffect)(()=>{let e=o.current;e&&(o.current=null,r(e))}),u}function d(e,t){if(e===t)return e;let r=l.get(e);if(r)return r.forEach(e=>e(t)),t;let n=l.get(t);return n?(n.forEach(t=>t(e)),e):t}function c(e=[]){let t=u(),[r,i]=function(e){let[t,r]=(0,a.useState)(e),i=(0,a.useRef)(null),s=(0,o.J)(()=>{if(!i.current)return;let e=i.current.next();if(e.done){i.current=null;return}t===e.value?s():r(e.value)});(0,n.N)(()=>{i.current&&s()});let l=(0,o.J)(e=>{i.current=e(t),s()});return[t,l]}(t),s=(0,a.useCallback)(()=>{i(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,i]);return(0,n.N)(s,[t,s,...e]),r}},8858:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(12115);let o="undefined"!=typeof document?n.useLayoutEffect:()=>{}},54807:(e,t,r)=>{r.d(t,{w:()=>o});var n=r(8858);function o(e,t){(0,n.N)(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}},43463:(e,t,r)=>{function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n,A:()=>o});let o=n},17539:(e,t,r)=>{r.d(t,{n:()=>n});let n="data-"+(0,r(42717).I)("framerAppearId")},5385:(e,t,r)=>{r.d(t,{N:()=>n});function n(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}},82043:(e,t,r)=>{r.d(t,{p:()=>n});let n=e=>Array.isArray(e)},25683:(e,t,r)=>{r.d(t,{N:()=>b});var n=r(95155),o=r(12115),a=r(64710),i=r(99234),s=r(39656),l=r(27249);class u extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:r}=e,a=(0,o.useId)(),i=(0,o.useRef)(null),s=(0,o.useRef)({width:0,height:0,top:0,left:0}),{nonce:d}=(0,o.useContext)(l.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o}=s.current;if(r||!i.current||!e||!t)return;i.current.dataset.motionPopId=a;let l=document.createElement("style");return d&&(l.nonce=d),document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(n,"px !important;\n            left: ").concat(o,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}},[r]),(0,n.jsx)(u,{isPresent:r,childRef:i,sizeRef:s,children:o.cloneElement(t,{ref:i})})}let c=e=>{let{children:t,initial:r,isPresent:a,onExitComplete:l,custom:u,presenceAffectsLayout:c,mode:p}=e,g=(0,i.M)(f),m=(0,o.useId)(),v=(0,o.useCallback)(e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;l&&l()},[g,l]),b=(0,o.useMemo)(()=>({id:m,initial:r,isPresent:a,custom:u,onExitComplete:v,register:e=>(g.set(e,!1),()=>g.delete(e))}),c?[Math.random(),v]:[a,v]);return(0,o.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[a]),o.useEffect(()=>{a||g.size||!l||l()},[a]),"popLayout"===p&&(t=(0,n.jsx)(d,{isPresent:a,children:t})),(0,n.jsx)(s.t.Provider,{value:b,children:t})};function f(){return new Map}var p=r(85087);let g=e=>e.key||"";function m(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}var v=r(35403);let b=e=>{let{children:t,custom:r,initial:s=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:d="sync",propagate:f=!1}=e,[b,y]=(0,p.xQ)(f),h=(0,o.useMemo)(()=>m(t),[t]),w=f&&!b?[]:h.map(g),x=(0,o.useRef)(!0),T=(0,o.useRef)(h),E=(0,i.M)(()=>new Map),[k,P]=(0,o.useState)(h),[C,S]=(0,o.useState)(h);(0,v.E)(()=>{x.current=!1,T.current=h;for(let e=0;e<C.length;e++){let t=g(C[e]);w.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[C,w.length,w.join("-")]);let M=[];if(h!==k){let e=[...h];for(let t=0;t<C.length;t++){let r=C[t],n=g(r);w.includes(n)||(e.splice(t,0,r),M.push(r))}"wait"===d&&M.length&&(e=M),S(m(e)),P(h);return}let{forceRender:j}=(0,o.useContext)(a.L);return(0,n.jsx)(n.Fragment,{children:C.map(e=>{let t=g(e),o=(!f||!!b)&&(h===C||w.includes(t));return(0,n.jsx)(c,{isPresent:o,initial:(!x.current||!!s)&&void 0,custom:o?void 0:r,presenceAffectsLayout:u,mode:d,onExitComplete:o?void 0:()=>{if(!E.has(t))return;E.set(t,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(null==j||j(),S(T.current),f&&(null==y||y()),l&&l())},children:e},t)})})}},85087:(e,t,r)=>{r.d(t,{xQ:()=>a});var n=r(12115),o=r(39656);function a(e=!0){let t=(0,n.useContext)(o.t);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:i,register:s}=t,l=(0,n.useId)();(0,n.useEffect)(()=>{e&&s(l)},[e]);let u=(0,n.useCallback)(()=>e&&i&&i(l),[l,i,e]);return!r&&i?[!1,u]:[!0]}},76498:(e,t,r)=>{r.d(t,{F:()=>s});var n=r(95155),o=r(12115),a=r(75815),i=r(94705);function s(e){let{children:t,features:r,strict:s=!1}=e,[,u]=(0,o.useState)(!l(r)),d=(0,o.useRef)(void 0);if(!l(r)){let{renderer:e,...t}=r;d.current=e,(0,i.Y)(t)}return(0,o.useEffect)(()=>{l(r)&&r().then(e=>{let{renderer:t,...r}=e;(0,i.Y)(r),d.current=t,u(!0)})},[]),(0,n.jsx)(a.Y.Provider,{value:{renderer:d.current,strict:s},children:t})}function l(e){return"function"==typeof e}},64710:(e,t,r)=>{r.d(t,{L:()=>n});let n=(0,r(12115).createContext)({})},75815:(e,t,r)=>{r.d(t,{Y:()=>n});let n=(0,r(12115).createContext)({strict:!1})},39656:(e,t,r)=>{r.d(t,{t:()=>n});let n=(0,r(12115).createContext)(null)},15750:(e,t,r)=>{r.d(t,{N:()=>n});let n=(0,r(12115).createContext)({})},14499:(e,t,r)=>{r.d(t,{I:()=>a});var n=r(93903);let o=["read","resolveKeyframes","update","preRender","render","postRender"];function a(e,t){let r=!1,a=!0,i={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,l=o.reduce((e,t)=>(e[t]=function(e){let t=new Set,r=new Set,n=!1,o=!1,a=new WeakSet,i={delta:0,timestamp:0,isProcessing:!1};function s(t){a.has(t)&&(l.schedule(t),e()),t(i)}let l={schedule:(e,o=!1,i=!1)=>{let s=i&&n?t:r;return o&&a.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(i=e,n){o=!0;return}n=!0,[t,r]=[r,t],t.forEach(s),t.clear(),n=!1,o&&(o=!1,l.process(e))}};return l}(s),e),{}),{read:u,resolveKeyframes:d,update:c,preRender:f,render:p,postRender:g}=l,m=()=>{let o=n.W.useManualTiming?i.timestamp:performance.now();r=!1,i.delta=a?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,u.process(i),d.process(i),c.process(i),f.process(i),p.process(i),g.process(i),i.isProcessing=!1,r&&t&&(a=!1,e(m))},v=()=>{r=!0,a=!0,i.isProcessing||e(m)};return{schedule:o.reduce((e,t)=>{let n=l[t];return e[t]=(e,t=!1,o=!1)=>(r||v(),n.schedule(e,t,o)),e},{}),cancel:e=>{for(let t=0;t<o.length;t++)l[o[t]].cancel(e)},state:i,steps:l}}},3307:(e,t,r)=>{r.d(t,{Gt:()=>o,PP:()=>s,WG:()=>a,uv:()=>i});var n=r(26054);let{schedule:o,cancel:a,state:i,steps:s}=(0,r(14499).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},45395:(e,t,r)=>{r.d(t,{k:()=>n});let{schedule:n,cancel:o}=(0,r(14499).I)(queueMicrotask,!1)},66247:(e,t,r)=>{r.d(t,{B:()=>o});let n={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},o={};for(let e in n)o[e]={isEnabled:t=>n[e].some(e=>!!t[e])}},94705:(e,t,r)=>{r.d(t,{Y:()=>o});var n=r(66247);function o(e){for(let t in e)n.B[t]={...n.B[t],...e[t]}}},59246:(e,t,r)=>{r.d(t,{z:()=>a});var n=r(63154),o=r(13480);function a(e,{layout:t,layoutId:r}){return o.f.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!n.H[e]||"opacity"===e)}},63154:(e,t,r)=>{r.d(t,{$:()=>o,H:()=>n});let n={};function o(e){Object.assign(n,e)}},82250:(e,t,r)=>{r.d(t,{C:()=>G});var n=r(95155),o=r(12115),a=r(64710),i=r(75815),s=r(27249);let l=(0,o.createContext)({});var u=r(62896),d=r(99038);function c(e){return Array.isArray(e)?e.join(" "):e}var f=r(15687),p=r(66247),g=r(94705);let m=Symbol.for("motionComponentSymbol");var v=r(78674),b=r(39656),y=r(35403),h=r(17539),w=r(45395),x=r(15750),T=r(75526),E=r(3307),k=r(5385),P=r(78442),C=r(99234),S=r(67365);let M=e=>(t,r)=>{let n=(0,o.useContext)(l),a=(0,o.useContext)(b.t),i=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:r},n,o,a){let i={latestValues:function(e,t,r,n){let o={},a=n(e,{});for(let e in a)o[e]=(0,S.u)(a[e]);let{initial:i,animate:s}=e,l=(0,d.e)(e),u=(0,d.O)(e);t&&u&&!l&&!1!==e.inherit&&(void 0===i&&(i=t.initial),void 0===s&&(s=t.animate));let c=!!r&&!1===r.initial,f=(c=c||!1===i)?s:i;if(f&&"boolean"!=typeof f&&!(0,k.N)(f)){let t=Array.isArray(f)?f:[f];for(let r=0;r<t.length;r++){let n=(0,P.a)(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(o[e]=t)}for(let t in e)o[t]=e[t]}}}return o}(n,o,a,e),renderState:t()};return r&&(i.onMount=e=>r({props:n,current:e,...i}),i.onUpdate=e=>r(e)),i})(e,t,n,a);return r?i():(0,C.M)(i)};var j=r(13480),O=r(71721);let I=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),L=()=>({...I(),attrs:{}});var A=r(7986),R=r(47928),z=r(72126);let N=["x","y","width","height","cx","cy","r"],$={useVisualState:M({scrapeMotionValuesFromProps:z.x,createRenderState:L,onUpdate:({props:e,prevProps:t,current:r,renderState:n,latestValues:o})=>{if(!r)return;let a=!!e.drag;if(!a){for(let e in o)if(j.f.has(e)){a=!0;break}}if(!a)return;let i=!t;if(t)for(let r=0;r<N.length;r++){let n=N[r];e[n]!==t[n]&&(i=!0)}i&&E.Gt.read(()=>{!function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}}(r,n),E.Gt.render(()=>{(0,O.B)(n,o,(0,A.n)(r.tagName),e.transformTemplate),(0,R.d)(r,n)})})}})},F={useVisualState:M({scrapeMotionValuesFromProps:r(701).x,createRenderState:I})};var D=r(59246),_=r(84707),K=r(67192);function W(e,t,r){for(let n in t)(0,_.S)(t[n])||(0,D.z)(n,r)||(e[n]=t[n])}var V=r(39970);function G(e,t){return function(r,{forwardMotionProps:E}={forwardMotionProps:!1}){return function(e){var t,r;let{preloadedFeatures:T,createVisualElement:E,useRender:k,useVisualState:P,Component:C}=e;function S(e,t){var r;let g;let m={...(0,o.useContext)(s.Q),...e,layoutId:function(e){let{layoutId:t}=e,r=(0,o.useContext)(a.L).id;return r&&void 0!==t?r+"-"+t:t}(e)},{isStatic:T}=m,S=function(e){let{initial:t,animate:r}=function(e,t){if((0,d.e)(e)){let{initial:t,animate:r}=e;return{initial:!1===t||(0,u.w)(t)?t:void 0,animate:(0,u.w)(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(l));return(0,o.useMemo)(()=>({initial:t,animate:r}),[c(t),c(r)])}(e),M=P(e,T);if(!T&&f.B){(0,o.useContext)(i.Y).strict;let e=function(e){let{drag:t,layout:r}=p.B;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(m);g=e.MeasureLayout,S.visualElement=function(e,t,r,n,a){var u,d;let{visualElement:c}=(0,o.useContext)(l),f=(0,o.useContext)(i.Y),p=(0,o.useContext)(b.t),g=(0,o.useContext)(s.Q).reducedMotion,m=(0,o.useRef)(null);n=n||f.renderer,!m.current&&n&&(m.current=n(e,{visualState:t,parent:c,props:r,presenceContext:p,blockInitialAnimation:!!p&&!1===p.initial,reducedMotionConfig:g}));let T=m.current,E=(0,o.useContext)(x.N);T&&!T.projection&&a&&("html"===T.type||"svg"===T.type)&&function(e,t,r,n){let{layoutId:o,layout:a,drag:i,dragConstraints:s,layoutScroll:l,layoutRoot:u}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:o,layout:a,alwaysMeasureLayout:!!i||s&&(0,v.X)(s),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(m.current,r,a,E);let k=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{T&&k.current&&T.update(r,p)});let P=r[h.n],C=(0,o.useRef)(!!P&&!(null===(u=window.MotionHandoffIsComplete)||void 0===u?void 0:u.call(window,P))&&(null===(d=window.MotionHasOptimisedAnimation)||void 0===d?void 0:d.call(window,P)));return(0,y.E)(()=>{T&&(k.current=!0,window.MotionIsMounted=!0,T.updateFeatures(),w.k.render(T.render),C.current&&T.animationState&&T.animationState.animateChanges())}),(0,o.useEffect)(()=>{T&&(!C.current&&T.animationState&&T.animationState.animateChanges(),C.current&&(queueMicrotask(()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,P)}),C.current=!1))}),T}(C,M,m,E,e.ProjectionNode)}return(0,n.jsxs)(l.Provider,{value:S,children:[g&&S.visualElement?(0,n.jsx)(g,{visualElement:S.visualElement,...m}):null,k(C,e,(r=S.visualElement,(0,o.useCallback)(e=>{e&&M.onMount&&M.onMount(e),r&&(e?r.mount(e):r.unmount()),t&&("function"==typeof t?t(e):(0,v.X)(t)&&(t.current=e))},[r])),M,T,S.visualElement)]})}T&&(0,g.Y)(T),S.displayName="motion.".concat("string"==typeof C?C:"create(".concat(null!==(r=null!==(t=C.displayName)&&void 0!==t?t:C.name)&&void 0!==r?r:"",")"));let M=(0,o.forwardRef)(S);return M[m]=C,M}({...(0,T.Q)(r)?$:F,preloadedFeatures:e,useRender:function(e=!1){return(t,r,n,{latestValues:a},i)=>{let s=((0,T.Q)(t)?function(e,t,r,n){let a=(0,o.useMemo)(()=>{let r=L();return(0,O.B)(r,t,(0,A.n)(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};W(t,e.style,e),a.style={...t,...a.style}}return a}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return W(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,o.useMemo)(()=>{let r=I();return(0,K.O)(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,a,i,t),l=(0,V.J)(r,"string"==typeof t,e),u=t!==o.Fragment?{...l,...s,ref:n}:{},{children:d}=r,c=(0,o.useMemo)(()=>(0,_.S)(d)?d.get():d,[d]);return(0,o.createElement)(t,{...u,children:c})}}(E),createVisualElement:t,Component:r})}}},90271:(e,t,r)=>{r.d(t,{I:()=>n});function n(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}},91307:(e,t,r)=>{r.d(t,{m:()=>a});var n=r(90271);let o=(0,r(82250).C)(),a=(0,n.I)(o)},42717:(e,t,r)=>{r.d(t,{I:()=>n});let n=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},12271:(e,t,r)=>{r.d(t,{j:()=>o,p:()=>i});let n=e=>t=>"string"==typeof t&&t.startsWith(e),o=n("--"),a=n("var(--"),i=e=>!!a(e)&&s.test(e.split("/*")[0].trim()),s=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},75526:(e,t,r)=>{r.d(t,{Q:()=>o});let n=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function o(e){if("string"!=typeof e||e.includes("-"));else if(n.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}},618:(e,t,r)=>{r.d(t,{W:()=>l});var n=r(41535),o=r(43406);let a={borderWidth:o.px,borderTopWidth:o.px,borderRightWidth:o.px,borderBottomWidth:o.px,borderLeftWidth:o.px,borderRadius:o.px,radius:o.px,borderTopLeftRadius:o.px,borderTopRightRadius:o.px,borderBottomRightRadius:o.px,borderBottomLeftRadius:o.px,width:o.px,maxWidth:o.px,height:o.px,maxHeight:o.px,top:o.px,right:o.px,bottom:o.px,left:o.px,padding:o.px,paddingTop:o.px,paddingRight:o.px,paddingBottom:o.px,paddingLeft:o.px,margin:o.px,marginTop:o.px,marginRight:o.px,marginBottom:o.px,marginLeft:o.px,backgroundPositionX:o.px,backgroundPositionY:o.px},i={rotate:o.uj,rotateX:o.uj,rotateY:o.uj,rotateZ:o.uj,scale:n.hs,scaleX:n.hs,scaleY:n.hs,scaleZ:n.hs,skew:o.uj,skewX:o.uj,skewY:o.uj,distance:o.px,translateX:o.px,translateY:o.px,translateZ:o.px,x:o.px,y:o.px,z:o.px,perspective:o.px,transformPerspective:o.px,opacity:n.X4,originX:o.gQ,originY:o.gQ,originZ:o.px},s={...n.ai,transform:Math.round},l={...a,...i,zIndex:s,size:o.px,fillOpacity:n.X4,strokeOpacity:n.X4,numOctaves:s}},67192:(e,t,r)=>{r.d(t,{O:()=>u});var n=r(12271);let o=(e,t)=>t&&"number"==typeof e?t.transform(e):e;var a=r(618),i=r(13480);let s={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},l=i.U.length;function u(e,t,r){let{style:u,vars:d,transformOrigin:c}=e,f=!1,p=!1;for(let e in t){let r=t[e];if(i.f.has(e)){f=!0;continue}if((0,n.j)(e)){d[e]=r;continue}{let t=o(r,a.W[e]);e.startsWith("origin")?(p=!0,c[e]=t):u[e]=t}}if(!t.transform&&(f||r?u.transform=function(e,t,r){let n="",u=!0;for(let d=0;d<l;d++){let l=i.U[d],c=e[l];if(void 0===c)continue;let f=!0;if(!(f="number"==typeof c?c===(l.startsWith("scale")?1:0):0===parseFloat(c))||r){let e=o(c,a.W[l]);if(!f){u=!1;let t=s[l]||l;n+=`${t}(${e}) `}r&&(t[l]=e)}}return n=n.trim(),r?n=r(t,u?"":n):u&&(n="none"),n}(t,e.transform,r):u.transform&&(u.transform="none")),p){let{originX:e="50%",originY:t="50%",originZ:r=0}=c;u.transformOrigin=`${e} ${t} ${r}`}}},13480:(e,t,r)=>{r.d(t,{U:()=>n,f:()=>o});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(n)},98535:(e,t,r)=>{r.d(t,{e:()=>n});function n(e,{style:t,vars:r},n,o){for(let a in Object.assign(e.style,t,o&&o.getProjectionStyles(n)),r)e.style.setProperty(a,r[a])}},701:(e,t,r)=>{r.d(t,{x:()=>a});var n=r(59246),o=r(84707);function a(e,t,r){var a;let{style:i}=e,s={};for(let l in i)((0,o.S)(i[l])||t.style&&(0,o.S)(t.style[l])||(0,n.z)(l,e)||(null===(a=null==r?void 0:r.getValue(l))||void 0===a?void 0:a.liveStyle)!==void 0)&&(s[l]=i[l]);return s}},71721:(e,t,r)=>{r.d(t,{B:()=>l});var n=r(67192),o=r(43406);let a={offset:"stroke-dashoffset",array:"stroke-dasharray"},i={offset:"strokeDashoffset",array:"strokeDasharray"};function s(e,t,r){return"string"==typeof e?e:o.px.transform(t+r*e)}function l(e,{attrX:t,attrY:r,attrScale:l,originX:u,originY:d,pathLength:c,pathSpacing:f=1,pathOffset:p=0,...g},m,v){if((0,n.O)(e,g,v),m){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:b,style:y,dimensions:h}=e;b.transform&&(h&&(y.transform=b.transform),delete b.transform),h&&(void 0!==u||void 0!==d||y.transform)&&(y.transformOrigin=function(e,t,r){let n=s(t,e.x,e.width),o=s(r,e.y,e.height);return`${n} ${o}`}(h,void 0!==u?u:.5,void 0!==d?d:.5)),void 0!==t&&(b.x=t),void 0!==r&&(b.y=r),void 0!==l&&(b.scale=l),void 0!==c&&function(e,t,r=1,n=0,s=!0){e.pathLength=1;let l=s?a:i;e[l.offset]=o.px.transform(-n);let u=o.px.transform(t),d=o.px.transform(r);e[l.array]=`${u} ${d}`}(b,c,f,p,!1)}},19552:(e,t,r)=>{r.d(t,{e:()=>n});let n=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"])},7986:(e,t,r)=>{r.d(t,{n:()=>n});let n=e=>"string"==typeof e&&"svg"===e.toLowerCase()},47928:(e,t,r)=>{r.d(t,{d:()=>i});var n=r(42717),o=r(98535),a=r(19552);function i(e,t,r,i){for(let r in(0,o.e)(e,t,void 0,i),t.attrs)e.setAttribute(a.e.has(r)?r:(0,n.I)(r),t.attrs[r])}},72126:(e,t,r)=>{r.d(t,{x:()=>i});var n=r(84707),o=r(13480),a=r(701);function i(e,t,r){let i=(0,a.x)(e,t,r);for(let r in e)((0,n.S)(e[r])||(0,n.S)(t[r]))&&(i[-1!==o.U.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return i}},99038:(e,t,r)=>{r.d(t,{O:()=>s,e:()=>i});var n=r(5385),o=r(62896),a=r(34543);function i(e){return(0,n.N)(e.animate)||a._.some(t=>(0,o.w)(e[t]))}function s(e){return!!(i(e)||e.variants)}},62896:(e,t,r)=>{r.d(t,{w:()=>n});function n(e){return"string"==typeof e||Array.isArray(e)}},78442:(e,t,r)=>{function n(e,t,r,n){if("function"==typeof t||("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t)){let[o,a]=function(e){let t=[{},{}];return null==e||e.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}(n);t=t(void 0!==r?r:e.custom,o,a)}return t}r.d(t,{a:()=>n})},34543:(e,t,r)=>{r.d(t,{U:()=>n,_:()=>o});let n=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],o=["initial",...n]},16611:(e,t,r)=>{r.d(t,{q:()=>n});let n=(e,t,r)=>r>t?t:r<e?e:r},15687:(e,t,r)=>{r.d(t,{B:()=>n});let n="undefined"!=typeof window},78674:(e,t,r)=>{r.d(t,{X:()=>n});function n(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}},55238:(e,t,r)=>{r.d(t,{B:()=>o,K:()=>a});var n=r(82043);let o=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),a=e=>(0,n.p)(e)?e[e.length-1]||0:e},35403:(e,t,r)=>{r.d(t,{E:()=>o});var n=r(12115);let o=r(15687).B?n.useLayoutEffect:n.useEffect},41535:(e,t,r)=>{r.d(t,{X4:()=>a,ai:()=>o,hs:()=>i});var n=r(16611);let o={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},a={...o,transform:e=>(0,n.q)(0,1,e)},i={...o,default:1}},43406:(e,t,r)=>{r.d(t,{KN:()=>a,gQ:()=>u,px:()=>i,uj:()=>o,vh:()=>s,vw:()=>l});let n=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),o=n("deg"),a=n("%"),i=n("px"),s=n("vh"),l=n("vw"),u={...a,parse:e=>a.parse(e)/100,transform:e=>a.transform(100*e)}},84707:(e,t,r)=>{r.d(t,{S:()=>n});let n=e=>!!(e&&e.getVelocity)},67365:(e,t,r)=>{r.d(t,{u:()=>a});var n=r(55238),o=r(84707);function a(e){let t=(0,o.S)(e)?e.get():e;return(0,n.B)(t)?t.toValue():t}},26054:(e,t,r)=>{r.d(t,{l:()=>n});let n=e=>e}}]);