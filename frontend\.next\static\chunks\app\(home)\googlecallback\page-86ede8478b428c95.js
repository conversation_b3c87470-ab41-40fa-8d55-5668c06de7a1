(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[982],{42559:(e,s,t)=>{Promise.resolve().then(t.bind(t,36825))},76046:(e,s,t)=>{"use strict";var n=t(66658);t.o(n,"notFound")&&t.d(s,{notFound:function(){return n.notFound}}),t.o(n,"useParams")&&t.d(s,{useParams:function(){return n.useParams}}),t.o(n,"useRouter")&&t.d(s,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(s,{useSearchParams:function(){return n.useSearchParams}})},36825:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,dynamic:()=>c});var n=t(95155),r=t(12115),o=t(76046),a=t(86934),u=t(30814);let c="force-dynamic";function h(e){let{}=e,s=(0,o.useRouter)(),[t,c]=(0,a.lT)(),h=(0,o.useSearchParams)().get("code");return h?((0,r.useEffect)(()=>{fetch("".concat("http://localhost:8000","/auth/google/"),{method:"POST",body:JSON.stringify({code:h}),headers:{"Content-Type":"application/json",accept:"application/json"}}).then(e=>{if(201===e.status)return e.json();u.o.error("فشل تسجيل الدخول، برجاء اعادة المحاولة"),s.push("/auth")}).then(e=>{c("access",e.access_token),c("refresh",e.refresh_token),u.o.success("تم تسجيل الدخول بنجاح"),s.refresh(),s.push("/")})},[h,s,c]),(0,n.jsx)("div",{className:"grid place-content-center h-full",children:(0,n.jsx)("p",{className:"font-bold text-2xl",children:"جارى تسجيل الدخول..."})})):(0,o.notFound)()}function i(e){let{}=e;return(0,n.jsx)(r.Suspense,{fallback:(0,n.jsx)("div",{children:"Loading..."}),children:(0,n.jsx)(h,{})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[814,934,441,898,358],()=>s(42559)),_N_E=e.O()}]);