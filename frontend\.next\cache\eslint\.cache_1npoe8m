[{"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx": "1", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx": "2", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\[id]\\page.tsx": "3", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\layout.tsx": "4", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx": "5", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\layout.tsx": "6", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\page.tsx": "7", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx": "8", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\layout.tsx": "9", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\page.tsx": "10", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\reset-password\\page.tsx": "11", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\layout.tsx": "12", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\ForgetPasswordForm.tsx": "13", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\LoginForm.tsx": "14", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\ResetPasswordForm.tsx": "15", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\SignUpForm.tsx": "16", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\global\\ClientProviders.tsx": "17", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\global\\Footer.tsx": "18", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\global\\Header.tsx": "19", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\AlertItemDetails\\page.tsx": "20", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx": "21", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx": "22", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\Chat\\index.tsx": "23", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx": "24", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\ui\\animated-modal.tsx": "25", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\ui\\file-upload.tsx": "26", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\lib\\data.ts": "27", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\lib\\utils.ts": "28", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\schemas\\application.ts": "29", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\schemas\\auth.ts": "30", "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\schemas\\messages.ts": "31"}, {"size": 386, "mtime": 1744025730000, "results": "32", "hashOfConfig": "33"}, {"size": 4755, "mtime": 1748079809000, "results": "34", "hashOfConfig": "33"}, {"size": 9027, "mtime": 1748079046986, "results": "35", "hashOfConfig": "33"}, {"size": 738, "mtime": 1748079244644, "results": "36", "hashOfConfig": "33"}, {"size": 1971, "mtime": 1748079198083, "results": "37", "hashOfConfig": "33"}, {"size": 2776, "mtime": 1748079303700, "results": "38", "hashOfConfig": "33"}, {"size": 4230, "mtime": 1748015948418, "results": "39", "hashOfConfig": "33"}, {"size": 861, "mtime": 1735494516000, "results": "40", "hashOfConfig": "33"}, {"size": 1530, "mtime": 1744563014000, "results": "41", "hashOfConfig": "33"}, {"size": 3502, "mtime": 1748016652415, "results": "42", "hashOfConfig": "33"}, {"size": 1091, "mtime": 1748011832000, "results": "43", "hashOfConfig": "33"}, {"size": 1132, "mtime": 1744571936000, "results": "44", "hashOfConfig": "33"}, {"size": 2190, "mtime": 1744412050000, "results": "45", "hashOfConfig": "33"}, {"size": 5418, "mtime": 1748016507462, "results": "46", "hashOfConfig": "33"}, {"size": 4777, "mtime": 1744545644000, "results": "47", "hashOfConfig": "33"}, {"size": 6601, "mtime": 1742070350000, "results": "48", "hashOfConfig": "33"}, {"size": 262, "mtime": 1732192774000, "results": "49", "hashOfConfig": "33"}, {"size": 4014, "mtime": 1748012382000, "results": "50", "hashOfConfig": "33"}, {"size": 2480, "mtime": 1744574634000, "results": "51", "hashOfConfig": "33"}, {"size": 3950, "mtime": 1744552192000, "results": "52", "hashOfConfig": "33"}, {"size": 3608, "mtime": 1744343474000, "results": "53", "hashOfConfig": "33"}, {"size": 2397, "mtime": 1735488810000, "results": "54", "hashOfConfig": "33"}, {"size": 7128, "mtime": 1748015545672, "results": "55", "hashOfConfig": "33"}, {"size": 10686, "mtime": 1744340788000, "results": "56", "hashOfConfig": "33"}, {"size": 6820, "mtime": 1744552016000, "results": "57", "hashOfConfig": "33"}, {"size": 10065, "mtime": 1735496490000, "results": "58", "hashOfConfig": "33"}, {"size": 284, "mtime": 1742069110000, "results": "59", "hashOfConfig": "33"}, {"size": 1776, "mtime": 1748015928191, "results": "60", "hashOfConfig": "33"}, {"size": 1319, "mtime": 1744571042000, "results": "61", "hashOfConfig": "33"}, {"size": 2006, "mtime": 1740975140000, "results": "62", "hashOfConfig": "33"}, {"size": 195, "mtime": 1744057918000, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "j4pvyv", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx", ["157", "158"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\[id]\\page.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\layout.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx", ["159", "160", "161"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\layout.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\page.tsx", ["162", "163", "164", "165", "166"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\layout.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\page.tsx", ["167"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\auth\\reset-password\\page.tsx", ["168", "169"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\layout.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\ForgetPasswordForm.tsx", ["170"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\LoginForm.tsx", ["171", "172"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\ResetPasswordForm.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\authentication\\SignUpForm.tsx", ["173", "174"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\global\\ClientProviders.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\global\\Footer.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\global\\Header.tsx", ["175", "176", "177"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\AlertItemDetails\\page.tsx", ["178", "179", "180", "181"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx", ["182"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\Chat\\index.tsx", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx", ["183", "184", "185"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\ui\\animated-modal.tsx", ["186"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\ui\\file-upload.tsx", ["187", "188", "189", "190"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\lib\\data.ts", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\lib\\utils.ts", ["191"], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\schemas\\application.ts", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\schemas\\auth.ts", [], [], "G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\schemas\\messages.ts", [], [], {"ruleId": "192", "severity": 1, "message": "193", "line": 3, "column": 31, "nodeType": null, "messageId": "194", "endLine": 3, "endColumn": 39}, {"ruleId": "192", "severity": 1, "message": "195", "line": 26, "column": 10, "nodeType": null, "messageId": "194", "endLine": 26, "endColumn": 31}, {"ruleId": "192", "severity": 1, "message": "196", "line": 14, "column": 12, "nodeType": null, "messageId": "194", "endLine": 14, "endColumn": 19}, {"ruleId": "197", "severity": 1, "message": "198", "line": 20, "column": 5, "nodeType": "199", "endLine": 20, "endColumn": 14}, {"ruleId": "192", "severity": 1, "message": "200", "line": 21, "column": 15, "nodeType": null, "messageId": "194", "endLine": 21, "endColumn": 18}, {"ruleId": "201", "severity": 1, "message": "202", "line": 10, "column": 13, "nodeType": "203", "messageId": "204", "endLine": 10, "endColumn": 16, "suggestions": "205"}, {"ruleId": "201", "severity": 1, "message": "202", "line": 11, "column": 19, "nodeType": "203", "messageId": "204", "endLine": 11, "endColumn": 22, "suggestions": "206"}, {"ruleId": "192", "severity": 1, "message": "207", "line": 15, "column": 5, "nodeType": null, "messageId": "194", "endLine": 15, "endColumn": 11}, {"ruleId": "192", "severity": 1, "message": "208", "line": 16, "column": 5, "nodeType": null, "messageId": "194", "endLine": 16, "endColumn": 17}, {"ruleId": "192", "severity": 1, "message": "209", "line": 18, "column": 11, "nodeType": null, "messageId": "194", "endLine": 18, "endColumn": 17}, {"ruleId": "197", "severity": 1, "message": "210", "line": 15, "column": 20, "nodeType": "199", "endLine": 15, "endColumn": 29}, {"ruleId": "201", "severity": 1, "message": "202", "line": 6, "column": 13, "nodeType": "203", "messageId": "204", "endLine": 6, "endColumn": 16, "suggestions": "211"}, {"ruleId": "201", "severity": 1, "message": "202", "line": 7, "column": 19, "nodeType": "203", "messageId": "204", "endLine": 7, "endColumn": 22, "suggestions": "212"}, {"ruleId": "192", "severity": 1, "message": "213", "line": 14, "column": 11, "nodeType": null, "messageId": "194", "endLine": 14, "endColumn": 17}, {"ruleId": "192", "severity": 1, "message": "196", "line": 18, "column": 12, "nodeType": null, "messageId": "194", "endLine": 18, "endColumn": 19}, {"ruleId": "192", "severity": 1, "message": "214", "line": 18, "column": 32, "nodeType": null, "messageId": "194", "endLine": 18, "endColumn": 44}, {"ruleId": "192", "severity": 1, "message": "215", "line": 6, "column": 10, "nodeType": null, "messageId": "194", "endLine": 6, "endColumn": 27}, {"ruleId": "192", "severity": 1, "message": "196", "line": 15, "column": 12, "nodeType": null, "messageId": "194", "endLine": 15, "endColumn": 19}, {"ruleId": "192", "severity": 1, "message": "216", "line": 11, "column": 32, "nodeType": null, "messageId": "194", "endLine": 11, "endColumn": 37}, {"ruleId": "192", "severity": 1, "message": "217", "line": 12, "column": 12, "nodeType": null, "messageId": "194", "endLine": 12, "endColumn": 13}, {"ruleId": "192", "severity": 1, "message": "218", "line": 12, "column": 15, "nodeType": null, "messageId": "194", "endLine": 12, "endColumn": 17}, {"ruleId": "192", "severity": 1, "message": "219", "line": 6, "column": 10, "nodeType": null, "messageId": "194", "endLine": 6, "endColumn": 15}, {"ruleId": "192", "severity": 1, "message": "220", "line": 6, "column": 17, "nodeType": null, "messageId": "194", "endLine": 6, "endColumn": 23}, {"ruleId": "192", "severity": 1, "message": "221", "line": 6, "column": 25, "nodeType": null, "messageId": "194", "endLine": 6, "endColumn": 28}, {"ruleId": "192", "severity": 1, "message": "222", "line": 26, "column": 7, "nodeType": null, "messageId": "194", "endLine": 26, "endColumn": 18}, {"ruleId": "192", "severity": 1, "message": "223", "line": 6, "column": 5, "nodeType": null, "messageId": "194", "endLine": 6, "endColumn": 17}, {"ruleId": "192", "severity": 1, "message": "224", "line": 16, "column": 10, "nodeType": null, "messageId": "194", "endLine": 16, "endColumn": 17}, {"ruleId": "192", "severity": 1, "message": "225", "line": 42, "column": 12, "nodeType": null, "messageId": "194", "endLine": 42, "endColumn": 26}, {"ruleId": "192", "severity": 1, "message": "226", "line": 89, "column": 18, "nodeType": null, "messageId": "194", "endLine": 89, "endColumn": 23}, {"ruleId": "201", "severity": 1, "message": "202", "line": 227, "column": 34, "nodeType": "203", "messageId": "204", "endLine": 227, "endColumn": 37, "suggestions": "227"}, {"ruleId": "192", "severity": 1, "message": "228", "line": 6, "column": 10, "nodeType": null, "messageId": "194", "endLine": 6, "endColumn": 12}, {"ruleId": "192", "severity": 1, "message": "229", "line": 7, "column": 8, "nodeType": null, "messageId": "194", "endLine": 7, "endColumn": 13}, {"ruleId": "230", "severity": 1, "message": "231", "line": 41, "column": 9, "nodeType": "232", "messageId": "233", "endLine": 41, "endColumn": 40}, {"ruleId": "230", "severity": 1, "message": "231", "line": 125, "column": 49, "nodeType": "232", "messageId": "233", "endLine": 125, "endColumn": 77}, {"ruleId": "192", "severity": 1, "message": "234", "line": 8, "column": 34, "nodeType": null, "messageId": "194", "endLine": 8, "endColumn": 35}, "@typescript-eslint/no-unused-vars", "'Suspense' is defined but never used.", "unusedVar", "'AdminChatsPageContent' is defined but never used.", "'cookies' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", "'res' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["235", "236"], ["237", "238"], "'params' is defined but never used.", "'searchParams' is defined but never used.", "'alerts' is assigned a value but never used.", "React Hook \"useRouter\" is called in function \"page\" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word \"use\".", ["239", "240"], ["241", "242"], "'router' is assigned a value but never used.", "'removeCookie' is assigned a value but never used.", "'ArrowUpWideNarrow' is defined but never used.", "'props' is defined but never used.", "'_' is assigned a value but never used.", "'__' is assigned a value but never used.", "'Popup' is defined but never used.", "'useMap' is defined but never used.", "'Map' is defined but never used.", "'middleOfUSA' is assigned a value but never used.", "'ModalContent' is defined but never used.", "'fetcher' is defined but never used.", "'selectedImages' is assigned a value but never used.", "'error' is defined but never used.", ["243", "244"], "'on' is defined but never used.", "'Image' is defined but never used.", "@typescript-eslint/no-unused-expressions", "Expected an assignment or function call and instead saw an expression.", "ExpressionStatement", "unusedExpression", "'J' is defined but never used.", {"messageId": "245", "fix": "246", "desc": "247"}, {"messageId": "248", "fix": "249", "desc": "250"}, {"messageId": "245", "fix": "251", "desc": "247"}, {"messageId": "248", "fix": "252", "desc": "250"}, {"messageId": "245", "fix": "253", "desc": "247"}, {"messageId": "248", "fix": "254", "desc": "250"}, {"messageId": "245", "fix": "255", "desc": "247"}, {"messageId": "248", "fix": "256", "desc": "250"}, {"messageId": "245", "fix": "257", "desc": "247"}, {"messageId": "248", "fix": "258", "desc": "250"}, "suggestUnknown", {"range": "259", "text": "260"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "261", "text": "262"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "263", "text": "260"}, {"range": "264", "text": "262"}, {"range": "265", "text": "260"}, {"range": "266", "text": "262"}, {"range": "267", "text": "260"}, {"range": "268", "text": "262"}, {"range": "269", "text": "260"}, {"range": "270", "text": "262"}, [377, 380], "unknown", [377, 380], "never", [400, 403], [400, 403], [178, 181], [178, 181], [201, 204], [201, 204], [6255, 6258], [6255, 6258]]