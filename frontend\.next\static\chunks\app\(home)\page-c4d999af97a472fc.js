(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[813],{48938:(e,t,s)=>{Promise.resolve().then(s.bind(s,80326)),Promise.resolve().then(s.t.bind(s,44839,23)),Promise.resolve().then(s.bind(s,50633)),Promise.resolve().then(s.bind(s,5083))},50633:(e,t,s)=>{"use strict";s.d(t,{AlertSection:()=>E});var r=s(95155),l=s(21567),a=s(25683),n=s(26424),i=s(12115);let c=(0,i.createContext)(void 0),o=e=>{let{children:t}=e,[s,l]=(0,i.useState)(!1);return(0,r.jsx)(c.Provider,{value:{open:s,setOpen:l},children:t})},d=()=>{let e=(0,i.useContext)(c);if(!e)throw Error("useModal must be used within a ModalProvider");return e};function x(e){let{children:t}=e;return(0,r.jsx)(o,{children:t})}let h=e=>{let{children:t,className:s}=e,{setOpen:a}=d();return(0,r.jsx)("button",{className:(0,l.cn)("px-4 py-2 rounded-md text-black dark:text-white text-center relative overflow-hidden",s),onClick:()=>a(!0),children:t})},m=e=>{let{children:t,className:s}=e,{open:c}=d();(0,i.useEffect)(()=>{c?document.body.style.overflow="hidden":document.body.style.overflow="auto"},[c]);let o=(0,i.useRef)(null),{setOpen:x}=d();return f(o,()=>x(!1)),(0,r.jsx)(a.N,{children:c&&(0,r.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:"fixed [perspective:800px] [transform-style:preserve-3d] inset-0 h-full w-full min-w-[25rem] flex items-center justify-center z-[99999]",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(n.P.div,{ref:o,className:(0,l.cn)("min-h-[50%] max-h-[90%] md:max-w-[40%] bg-white dark:bg-neutral-950 border border-transparent dark:border-neutral-800 md:rounded-2xl relative z-50 flex flex-col flex-1 overflow-hidden",s),initial:{opacity:0,scale:.5,rotateX:40,y:40},animate:{opacity:1,scale:1,rotateX:0,y:0},exit:{opacity:0,scale:.8,rotateX:10},transition:{type:"spring",stiffness:260,damping:15},children:[(0,r.jsx)(p,{}),t]})]})})},u=e=>{let{className:t}=e;return(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:"fixed inset-0 h-full w-full bg-black bg-opacity-50 z-50 ".concat(t)})},p=()=>{let{setOpen:e}=d();return(0,r.jsx)("button",{onClick:()=>e(!1),className:"absolute top-4 right-4 group",children:(0,r.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-black dark:text-white h-4 w-4 group-hover:scale-125 group-hover:rotate-3 transition duration-200",children:[(0,r.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,r.jsx)("path",{d:"M18 6l-12 12"}),(0,r.jsx)("path",{d:"M6 6l12 12"})]})})},f=(e,t)=>{(0,i.useEffect)(()=>{let s=s=>{!e.current||e.current.contains(s.target)||t(s)};return document.addEventListener("mousedown",s),document.addEventListener("touchstart",s),()=>{document.removeEventListener("mousedown",s),document.removeEventListener("touchstart",s)}},[e,t])};var j=s(25225),v=s(17281),b=s(77803),g=s(15325),N=s(29523),y=s(67396),w=s(42478);function k(e){let{id:t}=e,[s,a]=(0,i.useState)(!0),[n,c]=(0,i.useState)(null);return console.log(n),(0,i.useEffect)(()=>{(0,l.G)("/emergency/".concat(t,"/"),null,"GET").then(e=>e.json()).then(e=>{c(e)}).finally(()=>{a(!1)})},[t]),(0,r.jsx)("div",{className:"container mx-auto px-4 py-6 space-y-6",dir:"rtl",children:s?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[20rem]",children:(0,r.jsx)("p",{className:"font-bold text-xl",children:"جارى التحميل..."})}):n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-right mt-4",children:n.location}),(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed text-right",children:n.description}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("h2",{className:"text-sm font-semibold mb-3",children:"الصور المرفقة"}),(0,r.jsx)("div",{className:"grid grid-cols-3 gap-4 overflow-x-auto pb-4",dir:"ltr",children:n.images.map(e=>{let{id:t,image:s}=e;return(0,r.jsx)(w.W,{src:s||"/placeholder.svg",alt:"صورة ".concat(t+1),className:"w-[240px] h-[240px] object-cover rounded-lg"},t)})})]})]}):(0,r.jsx)("p",{children:"حدث خطأ أثناء تحميل الطلب برجاء اعادة المحاولة لاحقا"})})}function E(e){let{data:t,heading:s}=e;return(0,r.jsxs)("div",{className:"w-full max-w-[1200px] mx-auto px-4 py-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(y.default,{href:"/previous",className:"text-blue-500 hover:text-blue-600",children:(0,r.jsx)(g.A,{className:"w-5 h-5"})}),(0,r.jsx)("h2",{className:"text-xl font-bold",children:s})]})}),0===t.length?(0,r.jsx)("div",{className:"flex items-center justify-center w-full h-32",children:(0,r.jsx)("p",{className:"text-gray-500",children:"لا توجد تنبيهات حالياً"})}):(0,r.jsx)(j.H,{orientation:"horizontal",className:"flex gap-4 w-full overflow-x-auto pb-4",children:t.map(e=>(0,r.jsx)(v.Z,{className:"flex-none w-[300px] border border-gray-200",children:(0,r.jsxs)(b.U,{className:"gap-4",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-sm text-start",children:e.user_first_name+" "+e.user_last_name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.location})]}),(0,r.jsx)(N.A,{className:"w-5 h-5 text-blue-500 mt-1 flex-shrink-0"})]}),(0,r.jsx)("div",{className:"flex justify-end gap-2 border-t-1 pt-4",children:(0,r.jsxs)(x,{children:[(0,r.jsx)(h,{className:"bg-blue-600 text-sm text-white hover:opacity-75 transition",children:"عرض التفاصيل"}),(0,r.jsx)(m,{children:(0,r.jsx)(k,{id:e.id})})]},Math.random())})]})},e.id))})]})}},5083:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(95155),l=s(17281),a=s(77803),n=s(29329),i=s(25548),c=s(54333);function o(e){let{}=e;return(0,r.jsx)("section",{className:"py-12 bg-white",id:"call-us",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"اتصل بنا"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(l.Z,{children:(0,r.jsxs)(a.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,r.jsx)(n.A,{className:"w-6 h-6 text-blue-500"}),(0,r.jsx)("h3",{className:"font-bold",children:"الهاتف"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,r.jsx)("p",{dir:"ltr",children:"+970 2384501 / +970 2384501"}),(0,r.jsx)("p",{dir:"ltr",children:"+9702384501 / +970 2384501"})]})]})}),(0,r.jsx)(l.Z,{children:(0,r.jsxs)(a.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,r.jsx)(i.A,{className:"w-6 h-6 text-blue-500"}),(0,r.jsx)("h3",{className:"font-bold",children:"البريد الكتروني"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,r.jsx)("p",{children:"AssistanceFormat.Com.Eg"}),(0,r.jsx)("p",{children:"AssistanceFormat.Com.Eg"})]})]})}),(0,r.jsx)(l.Z,{children:(0,r.jsxs)(a.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,r.jsx)(c.A,{className:"w-6 h-6 text-blue-500"}),(0,r.jsx)("h3",{className:"font-bold",children:"العنوان الرئيسي"}),(0,r.jsxs)("p",{className:"text-sm text-default-500",children:["القدس - شارع مدينة",(0,r.jsx)("br",{}),"العربية - فلسطين"]})]})})]})]})})}s(12115)},21567:(e,t,s)=>{"use strict";s.d(t,{G:()=>n,cn:()=>a,f:()=>i});var r=s(43463),l=s(69795);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.QP)((0,r.$)(t))}async function n(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",r=arguments.length>3?arguments[3]:void 0;try{let l=await fetch("http://localhost:8000"+e,{method:s,body:null===t?null:JSON.stringify(t),headers:{"Content-Type":"application/json",Authorization:r?"Bearer ".concat(r):""}});if(!l.ok)return console.error("API error: ".concat(l.status," ").concat(l.statusText)),{json:()=>Promise.resolve({error:!0,status:l.status,message:"API error: ".concat(l.status," ").concat(l.statusText),results:[]})};return l}catch(e){return console.error("Fetch error:",e),{json:()=>Promise.resolve({error:!0,message:e instanceof Error?e.message:"Unknown error",results:[]})}}}let i=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},e=>{var t=t=>e(e.s=t);e.O(0,[13,936,746,446,30,396,859,441,898,358],()=>t(48938)),_N_E=e.O()}]);