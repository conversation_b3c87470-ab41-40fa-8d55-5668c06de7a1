(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{59930:(e,s,r)=>{Promise.resolve().then(r.bind(r,17260))},17260:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var a=r(95155),t=r(12115),o=r(69606),l=r(85060),n=r(97752),i=r(70632),c=r(65977),d=r(62113),u=r(94936),m=r(14078),p=r(66532),x=r(86934),h=r(21567),g=r(76046),j=r(30814);function v(){let e=(0,g.useRouter)(),[s,r]=(0,t.useState)(!1),[v,f,w]=(0,x.lT)(),{control:b,handleSubmit:y,formState:{errors:N,isSubmitting:I}}=(0,o.mN)({resolver:(0,l.u)(c.X5),defaultValues:{email:"",password:""}});async function T(s){try{var r;console.log("Login attempt with:",{email:s.email,passwordLength:null===(r=s.password)||void 0===r?void 0:r.length});let a={email:s.email,password:s.password},t=await (0,h.G)("/auth/jwt/create/",a,"POST");if(console.log("Login response status:",t.status),200===t.status){let s=await t.json();console.log("Login successful, tokens received"),s.access&&s.refresh?(f("access",s.access),f("refresh",s.refresh),j.o.success("تم تسجيل الدخول بنجاح"),e.push("/")):(console.error("Missing tokens in response:",s),j.o.error("حدث خطأ في تسجيل الدخول، الرجاء المحاولة مرة أخرى"))}else{let e=await t.json().catch(()=>({}));console.error("Login error:",t.status,e),400===t.status?j.o.error("بيانات غير صحيحة، تأكد من البريد الإلكتروني وكلمة المرور"):401===t.status?j.o.error("غير مصرح لك بالدخول، تأكد من بياناتك"):j.o.error("فشل تسجيل الدخول، الرجاء المحاولة مرة أخرى")}}catch(e){console.error("Login submission error:",e),j.o.error("حدث خطأ أثناء محاولة تسجيل الدخول")}}return(0,a.jsxs)("form",{onSubmit:y(T),className:"space-y-6 mt-6",children:[(0,a.jsx)(o.xI,{name:"email",control:b,render:e=>{var s;let{field:r}=e;return(0,a.jsx)(d.r,{...r,type:"email",label:"البريد الإلكتروني",variant:"bordered",isInvalid:!!N.email,errorMessage:null===(s=N.email)||void 0===s?void 0:s.message})}}),(0,a.jsx)(o.xI,{name:"password",control:b,render:e=>{var t;let{field:o}=e;return(0,a.jsx)(d.r,{...o,type:s?"text":"password",label:"كلمة المرور",variant:"bordered",isInvalid:!!N.password,errorMessage:null===(t=N.password)||void 0===t?void 0:t.message,endContent:(0,a.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>r(!s),children:s?(0,a.jsx)(n.A,{size:20}):(0,a.jsx)(i.A,{size:20})})})}}),(0,a.jsx)("div",{className:"flex items-center justify-end",children:(0,a.jsx)(u.T,{as:p.h,href:"/auth/forget-password",variant:"light",className:"px-0 font-normal",children:"هل نسيت كلمة المرور؟"})}),(0,a.jsx)(u.T,{type:"submit",color:"primary",className:(0,m.cn)("w-full",I?"opacity-50":""),disabled:I,children:"تسجيل الدخول"})]})}function f(){let[e,s]=(0,x.lT)(),r=(0,g.useRouter)(),[m,p]=(0,t.useState)(!1),[j,v]=(0,t.useState)(!1),[f,w]=(0,t.useState)(""),{control:b,handleSubmit:y,formState:{errors:N,isSubmitting:I}}=(0,o.mN)({resolver:(0,l.u)(c.Sd),defaultValues:{first_name:"",last_name:"",email:"",password:"",password2:""}});async function T(e){let a=await (0,h.G)("/users/",e,"POST"),t=await a.json();201!==a.status?w(Object.values(t)[0][0]):(s("access",t.access),s("refresh",t.refresh),r.push("/"))}return(0,a.jsxs)(a.Fragment,{children:[f&&(0,a.jsx)("p",{children:f[0].toUpperCase()+f.slice(1)}),(0,a.jsxs)("form",{onSubmit:y(T),className:"space-y-6 mt-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(o.xI,{name:"first_name",control:b,render:e=>{var s;let{field:r}=e;return(0,a.jsx)(d.r,{...r,label:"الاسم الاول",variant:"bordered",isInvalid:!!N.first_name,errorMessage:null===(s=N.first_name)||void 0===s?void 0:s.message})}}),(0,a.jsx)(o.xI,{name:"last_name",control:b,render:e=>{var s;let{field:r}=e;return(0,a.jsx)(d.r,{...r,label:"اسم العائلة",variant:"bordered",isInvalid:!!N.last_name,errorMessage:null===(s=N.last_name)||void 0===s?void 0:s.message})}})]}),(0,a.jsx)(o.xI,{name:"email",control:b,render:e=>{var s;let{field:r}=e;return(0,a.jsx)(d.r,{...r,type:"email",label:"البريد الالكتروني",variant:"bordered",isInvalid:!!N.email,errorMessage:null===(s=N.email)||void 0===s?void 0:s.message})}}),(0,a.jsx)(o.xI,{name:"password",control:b,render:e=>{var s;let{field:r}=e;return(0,a.jsx)(d.r,{...r,type:m?"text":"password",label:"كلمة المرور",variant:"bordered",isInvalid:!!N.password,errorMessage:null===(s=N.password)||void 0===s?void 0:s.message,endContent:(0,a.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>p(!m),children:m?(0,a.jsx)(n.A,{size:20}):(0,a.jsx)(i.A,{size:20})})})}}),(0,a.jsx)(o.xI,{name:"password2",control:b,render:e=>{var s;let{field:r}=e;return(0,a.jsx)(d.r,{...r,type:j?"text":"password",label:"تأكيد كلمة المرور",variant:"bordered",isInvalid:!!N.password2,errorMessage:null===(s=N.password2)||void 0===s?void 0:s.message,endContent:(0,a.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>v(!j),children:j?(0,a.jsx)(n.A,{size:20}):(0,a.jsx)(i.A,{size:20})})})}}),(0,a.jsx)(u.T,{type:"submit",color:"primary",className:(0,h.cn)("w-full",I?"opacity-50":""),disabled:I,children:"سجل الآن"})]})]})}var w=r(33557),b=r(21387),y=r(5565);function N(e){let{}=e,s=(0,g.useRouter)(),r=async()=>{try{let e=await (0,h.G)("/auth/google/url/",null,"GET");if(200===e.status){let r=await e.json();console.log("Google auth response:",r),r.url?s.push(r.url):(j.o.error("فشل تسجيل الدخول: لم يتم العثور على عنوان URL للتوجيه"),console.error("Missing URL in response:",r))}else j.o.error("فشل تسجيل الدخول: ".concat(e.status," ").concat(e.statusText)),console.error("Auth error:",e.status,e.statusText)}catch(e){console.error("Google sign-in error:",e),j.o.error("فشل تسجيل الدخول، برجاء اعادة المحاولة")}};return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"مرحباً بعودتك!"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"مرحباً بعودتك، من فضلك ادخل بياناتك."})]}),(0,a.jsxs)(w.r,{"aria-label":"Auth options",color:"primary",variant:"underlined",className:"w-full",children:[(0,a.jsx)(b.i,{title:"تسجيل الدخول",children:(0,a.jsx)(v,{})},"login"),(0,a.jsx)(b.i,{title:"انشاء حساب ",children:(0,a.jsx)(f,{})},"signup")]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("span",{className:"w-full border-t"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,a.jsx)("span",{className:"bg-white px-2 text-gray-500",children:"او اتصل باستخدام"})})]}),(0,a.jsxs)(u.T,{variant:"ghost",onPress:r,className:"border-1 w-full border-gray-200",children:[(0,a.jsx)("span",{className:"text-xl font-bold",children:"Google"}),(0,a.jsx)(y.default,{src:"/google-icon.png",alt:"Google",width:30,height:30})]})]})})}},21567:(e,s,r)=>{"use strict";r.d(s,{G:()=>l,cn:()=>o,f:()=>n});var a=r(43463),t=r(69795);function o(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,t.QP)((0,a.$)(s))}async function l(e,s){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",a=arguments.length>3?arguments[3]:void 0;try{let t=await fetch("http://localhost:8000"+e,{method:r,body:null===s?null:JSON.stringify(s),headers:{"Content-Type":"application/json",Authorization:a?"Bearer ".concat(a):""}});if(!t.ok)return console.error("API error: ".concat(t.status," ").concat(t.statusText)),{json:()=>Promise.resolve({error:!0,status:t.status,message:"API error: ".concat(t.status," ").concat(t.statusText),results:[]})};return t}catch(e){return console.error("Fetch error:",e),{json:()=>Promise.resolve({error:!0,message:e instanceof Error?e.message:"Unknown error",results:[]})}}}let n=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},65977:(e,s,r)=>{"use strict";r.d(s,{Ie:()=>l,Sd:()=>o,X5:()=>t,oW:()=>n});var a=r(10842);let t=a.Ik({email:a.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:a.Yj().min(1,{message:"كلمة المرور مطلوبة"})}),o=a.Ik({first_name:a.Yj().min(2,{message:"الاسم الأول يجب أن يكون على الأقل حرفين"}),last_name:a.Yj().min(2,{message:"اسم العائلة يجب أن يكون على الأقل حرفين"}),email:a.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:a.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),password2:a.Yj()}).refine(e=>e.password===e.password2,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]}),l=a.Ik({email:a.Yj().email({message:"البريد الإلكتروني غير صالح"}).optional().or(a.eu(""))}),n=a.Ik({password:a.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),confirmPassword:a.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]})}},e=>{var s=s=>e(e.s=s);e.O(0,[814,13,936,746,573,446,934,30,298,970,143,441,898,358],()=>s(59930)),_N_E=e.O()}]);