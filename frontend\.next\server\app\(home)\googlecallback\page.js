(()=>{var e={};e.id=982,e.ids=[982],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},85442:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var o=r(70260),n=r(28203),a=r(25155),s=r.n(a),i=r(67292),p={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);r.d(t,p);let l=["",{children:["(home)",{children:["googlecallback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,38009)),"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,36953)),"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(home)/googlecallback/page",pathname:"/googlecallback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},96708:(e,t,r)=>{Promise.resolve().then(r.bind(r,38009))},78148:(e,t,r)=>{Promise.resolve().then(r.bind(r,68461))},68461:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,dynamic:()=>i});var o=r(45512),n=r(58009),a=r(79334),s=r(45900);r(91542);let i="force-dynamic";function p({}){(0,a.useRouter)();let[e,t]=(0,s.lT)();return(0,a.useSearchParams)().get("code")?(0,o.jsx)("div",{className:"grid place-content-center h-full",children:(0,o.jsx)("p",{className:"font-bold text-2xl",children:"جارى تسجيل الدخول..."})}):(0,a.notFound)()}function l({}){return(0,o.jsx)(n.Suspense,{fallback:(0,o.jsx)("div",{children:"Loading..."}),children:(0,o.jsx)(p,{})})}},38009:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,dynamic:()=>n});var o=r(46760);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx","dynamic"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (3)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\googlecallback\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[989,368,944,956,263,597],()=>r(85442));module.exports=o})();