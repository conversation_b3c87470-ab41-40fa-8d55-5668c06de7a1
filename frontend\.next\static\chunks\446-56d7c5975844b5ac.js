"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[446],{73268:(t,e,i)=>{i.d(e,{f:()=>tP});var n=i(75713),r=i(40300),s=i(3307),a=i(93903);let o={current:!1};var l=i(26054);let u=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function h(t,e,i,n){if(t===e&&i===n)return l.l;let r=e=>(function(t,e,i,n,r){let s,a;let o=0;do(s=u(a=e+(i-e)/2,n,r)-t)>0?i=a:e=a;while(Math.abs(s)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:u(r(t),e,n)}var d=i(35290),p=i(94641);let c=h(.33,1.53,.69,.99),f=(0,p.G)(c),m=(0,d.V)(f),v=t=>(t*=2)<1?.5*f(t):.5*(2-Math.pow(2,-10*(t-1)));var y=i(26575),g=i(8216),b=i(79125),w=i(22368),S=i(45107),A=i(55254);let V=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(A.f.test(t)||"0"===t)&&!t.startsWith("url(")),T=t=>null!==t;function x(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(T),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return s&&void 0!==n?n:r[s]}class P{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=b.k.now(),this.options={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(0,w.q)(),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=b.k.now(),this.hasAttemptedResolve=!0;let{name:i,type:r,velocity:s,delay:a,onComplete:l,onUpdate:u,isGenerator:h}=this.options;if(!h&&!function(t,e,i,r){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let a=t[t.length-1],o=V(s,e),l=V(a,e);return(0,S.$)(o===l,`You are trying to animate ${e} from "${s}" to "${a}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${a} via the \`style\` property.`),!!o&&!!l&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||(0,n.WH)(i))&&r)}(t,i,r,s)){if(o.current||!a){u&&u(x(t,this.options,e)),l&&l(),this.resolveFinishedPromise();return}this.options.duration=0}let d=this.initPlayback(t,e);!1!==d&&(this._resolved={keyframes:t,finalKeyframe:e,...d},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}var M=i(16611),k=i(50010);function C(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var F=i(98967),E=i(2664),O=i(83020);function I(t,e){return i=>i>0?e:t}let D=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},N=[F.u,E.B,O.V],$=t=>N.find(e=>e.test(t));function R(t){let e=$(t);if((0,S.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===O.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;r=C(o,n,t+1/3),s=C(o,n,t),a=C(o,n,t-1/3)}else r=s=a=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*a),alpha:n}}(i)),i}let K=(t,e)=>{let i=R(t),n=R(e);if(!i||!n)return I(t,e);let r={...i};return t=>(r.red=D(i.red,n.red,t),r.green=D(i.green,n.green,t),r.blue=D(i.blue,n.blue,t),r.alpha=(0,k.k)(i.alpha,n.alpha,t),E.B.transform(r))};var B=i(15068),q=i(94064),L=i(12271);let U=new Set(["none","hidden"]);function W(t,e){return i=>(0,k.k)(t,e,i)}function G(t){return"number"==typeof t?W:"string"==typeof t?(0,L.p)(t)?I:q.y.test(t)?K:H:Array.isArray(t)?X:"object"==typeof t?q.y.test(t)?K:j:I}function X(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>G(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function j(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=G(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let H=(t,e)=>{let i=A.f.createTransformer(e),n=(0,A.V)(t),r=(0,A.V)(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?U.has(t)&&!r.values.length||U.has(e)&&!n.values.length?function(t,e){return U.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,B.F)(X(function(t,e){var i;let n=[],r={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let a=e.types[s],o=t.indexes[a][r[a]],l=null!==(i=t.values[o])&&void 0!==i?i:0;n[s]=l,r[a]++}return n}(n,r),r.values),i):((0,S.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),I(t,e))};function Y(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,k.k)(t,e,i):G(t)(t,e)}var _=i(42206);function z(t,e,i){let n=Math.max(e-5,0);return(0,_.f)(i-t(n),e-n)}let Q={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function J(t,e){return t*Math.sqrt(1-e*e)}let Z=["duration","bounce"],tt=["stiffness","damping","mass"];function te(t,e){return e.some(e=>void 0!==t[e])}function ti(t=Q.visualDuration,e=Q.bounce){let i;let s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:a,restDelta:o}=s,l=s.keyframes[0],u=s.keyframes[s.keyframes.length-1],h={done:!1,value:l},{stiffness:d,damping:p,mass:c,duration:f,velocity:m,isResolvedFromDuration:v}=function(t){let e={velocity:Q.velocity,stiffness:Q.stiffness,damping:Q.damping,mass:Q.mass,isResolvedFromDuration:!1,...t};if(!te(t,tt)&&te(t,Z)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*(0,M.q)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:Q.mass,stiffness:n,damping:r}}else{let i=function({duration:t=Q.duration,bounce:e=Q.bounce,velocity:i=Q.velocity,mass:n=Q.mass}){let s,a;(0,S.$)(t<=(0,r.f)(Q.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=(0,M.q)(Q.minDamping,Q.maxDamping,o),t=(0,M.q)(Q.minDuration,Q.maxDuration,(0,r.X)(t)),o<1?(s=e=>{let n=e*o,r=n*t;return .001-(n-i)/J(e,o)*Math.exp(-r)},a=e=>{let n=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=J(Math.pow(e,2),o);return(n*i+i-r)*a*(-s(e)+.001>0?-1:1)/l}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let l=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,a,5/t);if(t=(0,r.f)(t),isNaN(l))return{stiffness:Q.stiffness,damping:Q.damping,duration:t};{let e=Math.pow(l,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:Q.mass}).isResolvedFromDuration=!0}}return e}({...s,velocity:-(0,r.X)(s.velocity||0)}),y=m||0,g=p/(2*Math.sqrt(d*c)),b=u-l,w=(0,r.X)(Math.sqrt(d/c)),A=5>Math.abs(b);if(a||(a=A?Q.restSpeed.granular:Q.restSpeed.default),o||(o=A?Q.restDelta.granular:Q.restDelta.default),g<1){let t=J(w,g);i=e=>u-Math.exp(-g*w*e)*((y+g*w*b)/t*Math.sin(t*e)+b*Math.cos(t*e))}else if(1===g)i=t=>u-Math.exp(-w*t)*(b+(y+w*b)*t);else{let t=w*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*w*e),n=Math.min(t*e,300);return u-i*((y+g*w*b)*Math.sinh(n)+t*b*Math.cosh(n))/t}}let V={calculatedDuration:v&&f||null,next:t=>{let e=i(t);if(v)h.done=t>=f;else{let n=0;g<1&&(n=0===t?(0,r.f)(y):z(i,t,e));let s=Math.abs(n)<=a,l=Math.abs(u-e)<=o;h.done=s&&l}return h.value=h.done?u:e,h},toString:()=>{let t=Math.min((0,n.tu)(V),n.YE),e=(0,n.KZ)(e=>V.next(t*e).value,t,30);return t+"ms "+e}};return V}function tn({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let d,p;let c=t[0],f={done:!1,value:c},m=t=>void 0!==o&&t<o||void 0!==l&&t>l,v=t=>void 0===o?l:void 0===l?o:Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,g=c+y,b=void 0===a?g:a(g);b!==g&&(y=b-c);let w=t=>-y*Math.exp(-t/n),S=t=>b+w(t),A=t=>{let e=w(t),i=S(t);f.done=Math.abs(e)<=u,f.value=f.done?b:i},V=t=>{m(f.value)&&(d=t,p=ti({keyframes:[f.value,v(f.value)],velocity:z(S,t,f.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return V(0),{calculatedDuration:null,next:t=>{let e=!1;return(p||void 0!==d||(e=!0,A(t),V(t)),void 0!==d&&t>=d)?p.next(t-d):(e||A(t),f)}}}let tr=h(.42,0,1,1),ts=h(0,0,.58,1),ta=h(.42,0,.58,1),to=t=>Array.isArray(t)&&"number"!=typeof t[0],tl={linear:l.l,easeIn:tr,easeInOut:ta,easeOut:ts,circIn:y.po,circInOut:y.tn,circOut:y.yT,backIn:f,backInOut:m,backOut:c,anticipate:v},tu=t=>{if((0,n.DW)(t)){(0,S.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return h(e,i,n,r)}return"string"==typeof t?((0,S.V)(void 0!==tl[t],`Invalid easing type '${t}'`),tl[t]):t};var th=i(19615);function td({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=to(n)?n.map(tu):tu(n),s={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if((0,S.V)(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let n=[],r=i||Y,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||l.l:e;s=(0,B.F)(t,s)}n.push(s)}return n}(e,n,r),u=o.length,h=i=>{if(a&&i<t[0])return e[0];let n=0;if(u>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=(0,th.q)(t[n],t[n+1],i);return o[n](r)};return i?e=>h((0,M.q)(t[0],t[s-1],e)):h}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=(0,th.q)(0,e,n);t.push((0,k.k)(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(r)?r:e.map(()=>r||ta).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=a(e),s.done=e>=t,s)}}let tp=t=>{let e=({timestamp:e})=>t(e);return{start:()=>s.Gt.update(e,!0),stop:()=>(0,s.WG)(e),now:()=>s.uv.isProcessing?s.uv.timestamp:b.k.now()}},tc={decay:tn,inertia:tn,tween:td,keyframes:td,spring:ti},tf=t=>t/100;class tm extends P{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:n,keyframes:r}=this.options,s=(null==n?void 0:n.KeyframeResolver)||w.h;this.resolver=new s(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:r="keyframes",repeat:s=0,repeatDelay:a=0,repeatType:o,velocity:l=0}=this.options,u=(0,n.WH)(r)?r:tc[r]||td;u!==td&&"number"!=typeof t[0]&&(e=(0,B.F)(tf,Y(t[0],t[1])),t=[0,100]);let h=u({...this.options,keyframes:t});"mirror"===o&&(i=u({...this.options,keyframes:[...t].reverse(),velocity:-l})),null===h.calculatedDuration&&(h.calculatedDuration=(0,n.tu)(h));let{calculatedDuration:d}=h,p=d+a;return{generator:h,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:d,resolvedDuration:p,totalDuration:p*(s+1)-a}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:n,generator:r,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:o,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return r.next(0);let{delay:d,repeat:p,repeatType:c,repeatDelay:f,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),y=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let g=this.currentTime,b=r;if(p){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,p+1))%2&&("reverse"===c?(i=1-i,f&&(i-=f/h)):"mirror"===c&&(b=s)),g=(0,M.q)(0,1,i)*h}let w=y?{done:!1,value:o[0]}:b.next(g);a&&(w.value=a(w.value));let{done:S}=w;y||null===l||(S=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return A&&void 0!==n&&(w.value=x(o,this.options,n)),m&&m(w.value),A&&this.finish(),w}get duration(){let{resolved:t}=this;return t?(0,r.X)(t.calculatedDuration):0}get time(){return(0,r.X)(this.currentTime)}set time(t){t=(0,r.f)(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,r.X)(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=tp,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let tv=new Set(["opacity","clipPath","filter","transform"]),ty=(0,i(64996).p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),tg={anticipate:v,backInOut:m,circInOut:y.tn};class tb extends P{constructor(t){super(t);let{name:e,motionValue:i,element:n,keyframes:r}=this.options;this.resolver=new g.K(r,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:r=300,times:s,ease:a,type:o,motionValue:l,name:u,startTime:h}=this.options;if(!l.owner||!l.owner.current)return!1;if("string"==typeof a&&(0,n.nL)()&&a in tg&&(a=tg[a]),i=this.options,(0,n.WH)(i.type)||"spring"===i.type||!(0,n.yL)(i.ease)){let{onComplete:e,onUpdate:i,motionValue:n,element:l,...u}=this.options,h=function(t,e){let i=new tm({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:t[0]},r=[],s=0;for(;!n.done&&s<2e4;)r.push((n=i.sample(s)).value),s+=10;return{times:void 0,keyframes:r,duration:s-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),r=h.duration,s=h.times,a=h.ease,o="keyframes"}let d=function(t,e,i,{delay:r=0,duration:s=300,repeat:a=0,repeatType:o="loop",ease:l="easeInOut",times:u}={}){let h={[e]:i};u&&(h.offset=u);let d=(0,n.TU)(l,s);return Array.isArray(d)&&(h.easing=d),t.animate(h,{delay:r,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"})}(l.owner.current,u,t,{...this.options,duration:r,times:s,ease:a});return d.startTime=null!=h?h:this.calcStartTime(),this.pendingTimeline?((0,n.vG)(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{let{onComplete:i}=this.options;l.set(x(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:r,times:s,type:o,ease:a,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return(0,r.X)(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return(0,r.X)(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=(0,r.f)(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return l.l;let{animation:i}=e;(0,n.vG)(i,t)}else this.pendingTimeline=t;return l.l}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:n,type:s,ease:a,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:l,element:u,...h}=this.options,d=new tm({...h,keyframes:i,duration:n,type:s,ease:a,times:o,isGenerator:!0}),p=(0,r.f)(this.time);t.setWithVelocity(d.sample(p-10).value,d.sample(p).value,10)}let{onStop:l}=this.options;l&&l(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:a}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return ty()&&i&&tv.has(i)&&!o&&!l&&!n&&"mirror"!==r&&0!==s&&"inertia"!==a}}var tw=i(13480);let tS={type:"spring",stiffness:500,damping:25,restSpeed:10},tA=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),tV={type:"keyframes",duration:.8},tT={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tx=(t,{keyframes:e})=>e.length>2?tV:tw.f.has(t)?t.startsWith("scale")?tA(e[1]):tS:tT,tP=(t,e,i,l={},u,h)=>d=>{let p=(0,n.rU)(l,t)||{},c=p.delay||l.delay||0,{elapsed:f=0}=l;f-=(0,r.f)(c);let m={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...p,delay:-f,onUpdate:t=>{e.set(t),p.onUpdate&&p.onUpdate(t)},onComplete:()=>{d(),p.onComplete&&p.onComplete()},name:t,motionValue:e,element:h?void 0:u};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(p)&&(m={...m,...tx(t,m)}),m.duration&&(m.duration=(0,r.f)(m.duration)),m.repeatDelay&&(m.repeatDelay=(0,r.f)(m.repeatDelay)),void 0!==m.from&&(m.keyframes[0]=m.from);let v=!1;if(!1!==m.type&&(0!==m.duration||m.repeatDelay)||(m.duration=0,0!==m.delay||(v=!0)),(o.current||a.W.skipAnimations)&&(v=!0,m.duration=0,m.delay=0),v&&!h&&void 0!==e.get()){let t=x(m.keyframes,p);if(void 0!==t)return s.Gt.update(()=>{m.onUpdate(t),m.onComplete()}),new n.P6([])}return!h&&tb.supports(m)?new tb(m):new tm(m)}},32389:(t,e,i)=>{i.d(e,{P:()=>r});var n=i(17539);function r(t){return t.props[n.n]}},26575:(t,e,i)=>{i.d(e,{po:()=>s,tn:()=>o,yT:()=>a});var n=i(35290),r=i(94641);let s=t=>1-Math.sin(Math.acos(t)),a=(0,r.G)(s),o=(0,n.V)(s)},35290:(t,e,i)=>{i.d(e,{V:()=>n});let n=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},94641:(t,e,i)=>{i.d(e,{G:()=>n});let n=t=>e=>1-t(1-e)},85677:(t,e,i)=>{i.d(e,{k:()=>n});function n(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}},66203:(t,e,i)=>{i.d(e,{F:()=>s,e:()=>r});var n=i(75713);function r(t){return{point:{x:t.pageX,y:t.pageY}}}let s=t=>e=>(0,n.Mc)(e)&&t(e,r(e))},79125:(t,e,i)=>{let n;i.d(e,{k:()=>o});var r=i(93903),s=i(3307);function a(){n=void 0}let o={now:()=>(void 0===n&&o.set(s.uv.isProcessing||r.W.useManualTiming?s.uv.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(a)}}},35971:(t,e,i)=>{i.d(e,{X:()=>n});class n{constructor(t){this.isMounted=!1,this.node=t}update(){}}},28456:(t,e,i)=>{i.d(e,{W:()=>F});var n=i(5385),r=i(82043);function s(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}var a=i(62896),o=i(78442);function l(t,e,i){let n=t.getProps();return(0,o.a)(n,e,void 0!==i?i:n.custom,t)}var u=i(34543),h=i(75713),d=i(67287),p=i(55238),c=i(93250),f=i(86901),m=i(32389),v=i(73268),y=i(3307);function g(t,e,{delay:i=0,transitionOverride:n,type:r}={}){var s;let{transition:a=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(a=n);let b=[],w=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,null!==(s=t.latestValues[e])&&void 0!==s?s:null),r=u[e];if(void 0===r||w&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(w,e))continue;let o={delay:i,...(0,h.rU)(a||{},e)},l=!1;if(window.MotionHandoffAnimation){let i=(0,m.P)(t);if(i){let t=window.MotionHandoffAnimation(i,e,y.Gt);null!==t&&(o.startTime=t,l=!0)}}(0,f.g)(t,e),n.start((0,v.f)(e,n,r,t.shouldReduceMotion&&d.$.has(e)?{type:!1}:o,t,l));let p=n.animation;p&&b.push(p)}return o&&Promise.all(b).then(()=>{y.Gt.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=l(t,e)||{};for(let e in r={...r,...i}){let i=(0,p.K)(r[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,c.OQ)(i))}}(t,o)})}),b}function b(t,e,i={}){var n;let r=l(t,e,"exit"===i.type?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let a=r?()=>Promise.all(g(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,r=1,s){let a=[],o=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>o-t*n;return Array.from(t.variantChildren).sort(w).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(b(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+n,a,o,i)}:()=>Promise.resolve(),{when:u}=s;if(!u)return Promise.all([a(),o(i.delay)]);{let[t,e]="beforeChildren"===u?[a,o]:[o,a];return t().then(()=>e())}}function w(t,e){return t.sortNodePosition(e)}let S=u._.length,A=[...u.U].reverse(),V=u.U.length;function T(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function x(){return{animate:T(!0),whileInView:T(),whileHover:T(),whileTap:T(),whileDrag:T(),whileFocus:T(),exit:T()}}var P=i(35971);class M extends P.X{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>b(t,e,i)));else if("string"==typeof e)n=b(t,e,i);else{let r="function"==typeof e?l(t,e,i.custom):e;n=Promise.all(g(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=x(),o=!0,h=e=>(i,n)=>{var r;let s=l(t,n,"exit"===e?null===(r=t.presenceContext)||void 0===r?void 0:r.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function d(l){let{props:d}=t,p=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<S;t++){let n=u._[t],r=e.props[n];((0,a.w)(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},c=[],f=new Set,m={},v=1/0;for(let e=0;e<V;e++){var y;let u=A[e],g=i[u],b=void 0!==d[u]?d[u]:p[u],w=(0,a.w)(b),S=u===l?g.isActive:null;!1===S&&(v=e);let V=b===p[u]&&b!==d[u]&&w;if(V&&o&&t.manuallyAnimateOnMount&&(V=!1),g.protectedKeys={...m},!g.isActive&&null===S||!b&&!g.prevProp||(0,n.N)(b)||"boolean"==typeof b)continue;let T=(y=g.prevProp,"string"==typeof b?b!==y:!!Array.isArray(b)&&!s(b,y)),x=T||u===l&&g.isActive&&!V&&w||e>v&&w,P=!1,M=Array.isArray(b)?b:[b],k=M.reduce(h(u),{});!1===S&&(k={});let{prevResolvedValues:C={}}=g,F={...C,...k},E=e=>{x=!0,f.has(e)&&(P=!0,f.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in F){let e=k[t],i=C[t];if(!m.hasOwnProperty(t))((0,r.p)(e)&&(0,r.p)(i)?s(e,i):e===i)?void 0!==e&&f.has(t)?E(t):g.protectedKeys[t]=!0:null!=e?E(t):f.add(t)}g.prevProp=b,g.prevResolvedValues=k,g.isActive&&(m={...m,...k}),o&&t.blockInitialAnimation&&(x=!1);let O=!(V&&T)||P;x&&O&&c.push(...M.map(t=>({animation:t,options:{type:u}})))}if(f.size){let e={};f.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=null!=n?n:null}),c.push({animation:e})}let g=!!c.length;return o&&(!1===d.initial||d.initial===d.animate)&&!t.manuallyAnimateOnMount&&(g=!1),o=!1,g?e(c):Promise.resolve()}return{animateChanges:d,setActive:function(e,n){var r;if(i[e].isActive===n)return Promise.resolve();null===(r=t.variantChildren)||void 0===r||r.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,n)}),i[e].isActive=n;let s=d(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=x(),o=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,n.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let k=0;class C extends P.X{constructor(){super(...arguments),this.id=k++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let F={animation:{Feature:M},exit:{Feature:C}}},89806:(t,e,i)=>{i.d(e,{n:()=>w});var n=i(35971),r=i(75713),s=i(66203),a=i(3307);function o(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&a.Gt.postRender(()=>r(e,(0,s.e)(e)))}class l extends n.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,r.PT)(t,t=>(o(this.node,t,"Start"),t=>o(this.node,t,"End"))))}unmount(){}}var u=i(85677),h=i(15068);class d extends n.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,h.F)((0,u.k)(this.node.current,"focus",()=>this.onFocus()),(0,u.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function p(t,e,i){let{props:n}=t;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&a.Gt.postRender(()=>r(e,(0,s.e)(e)))}class c extends n.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,r.c$)(t,t=>(p(this.node,t,"Start"),(t,{success:e})=>p(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let f=new WeakMap,m=new WeakMap,v=t=>{let e=f.get(t.target);e&&e(t)},y=t=>{t.forEach(v)},g={some:0,all:1};class b extends n.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:g[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;m.has(i)||m.set(i,{});let n=m.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(y,{root:t,...e})),n[r]}(e);return f.set(t,i),n.observe(t),()=>{f.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let w={inView:{Feature:b},tap:{Feature:c},focus:{Feature:d},hover:{Feature:l}}},65599:(t,e,i)=>{function n({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function r({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function s(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}i.d(e,{FY:()=>n,bS:()=>s,pA:()=>r})},32994:(t,e,i)=>{i.d(e,{OU:()=>u,Ql:()=>h,Ww:()=>p,hq:()=>s,o4:()=>l});var n=i(50010),r=i(62985);function s(t,e,i){return i+e*(t-i)}function a(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function o(t,e=0,i=1,n,r){t.min=a(t.min,e,i,n,r),t.max=a(t.max,e,i,n,r)}function l(t,{x:e,y:i}){o(t.x,e.translate,e.scale,e.originPoint),o(t.y,i.translate,i.scale,i.originPoint)}function u(t,e,i,n=!1){let s,a;let o=i.length;if(o){e.x=e.y=1;for(let u=0;u<o;u++){a=(s=i[u]).projectionDelta;let{visualElement:o}=s.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&p(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,l(t,a)),n&&(0,r.HD)(s.latestValues)&&p(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function h(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,r,s=.5){let a=(0,n.k)(t.min,t.max,s);o(t,e,i,a,r)}function p(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},27865:(t,e,i)=>{i.d(e,{ge:()=>a,xU:()=>r});let n=()=>({translate:0,scale:1,origin:0,originPoint:0}),r=()=>({x:n(),y:n()}),s=()=>({min:0,max:0}),a=()=>({x:s(),y:s()})},62985:(t,e,i)=>{function n(t){return void 0===t||1===t}function r({scale:t,scaleX:e,scaleY:i}){return!n(t)||!n(e)||!n(i)}function s(t){return r(t)||a(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function a(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>s,vF:()=>a,vk:()=>r})},77106:(t,e,i)=>{i.d(e,{L:()=>a,m:()=>s});var n=i(65599),r=i(32994);function s(t,e){return(0,n.FY)((0,n.bS)(t.getBoundingClientRect(),e))}function a(t,e,i){let n=s(t,i),{scroll:a}=e;return a&&((0,r.Ql)(n.x,a.offset.x),(0,r.Ql)(n.y,a.offset.y)),n}},8216:(t,e,i)=>{i.d(e,{K:()=>m});var n=i(66879),r=i(67287),s=i(55254),a=i(1435);let o=new Set(["auto","none","0"]);var l=i(22368),u=i(45107),h=i(20555),d=i(12271);let p=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var c=i(40403),f=i(18201);class m extends l.h{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,d.p)(n))){let r=function t(e,i,n=1){(0,u.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=p.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${null!=i?i:n}`,r]}(e);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let t=a.trim();return(0,h.i)(t)?parseFloat(t):t}return(0,d.p)(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!r.$.has(i)||2!==t.length)return;let[n,s]=t,a=(0,f.n)(n),o=(0,f.n)(s);if(a!==o){if((0,c.E4)(a)&&(0,c.E4)(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||(0,n.$)(r))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!o.has(e)&&(0,s.V)(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=(0,a.J)(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=c.Hr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(i);r&&r.jump(this.measuredOrigin,!1);let s=n.length-1,a=n[s];n[s]=c.Hr[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}},78314:(t,e,i)=>{i.d(e,{J:()=>H});var n=i(12115),r=i(77106),s=i(79125),a=i(66247),o=i(27865),l=i(20555),u=i(66879),h=i(15687);let d={current:null},p={current:!1};var c=i(87945),f=i(93250),m=i(55254),v=i(84707),y=i(1435),g=i(94064),b=i(18201),w=i(83510);let S=[...b.T,g.y,m.f],A=t=>S.find((0,w.w)(t));var V=i(13480);let T=new WeakMap;var x=i(99038),P=i(22368),M=i(78442),k=i(3307);let C=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class F{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=P.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=s.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,k.Gt.render(this.render,!1,!0))};let{latestValues:l,renderState:u,onUpdate:h}=a;this.onUpdate=h,this.latestValues=l,this.baseTarget={...l},this.initialValues=e.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=(0,x.e)(e),this.isVariantNode=(0,x.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:d,...p}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in p){let e=p[t];void 0!==l[t]&&(0,v.S)(e)&&e.set(l[t],!1)}}mount(t){this.current=t,T.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),p.current||function(){if(p.current=!0,h.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>d.current=t.matches;t.addListener(e),e()}else d.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||d.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in T.delete(this.current),this.projection&&this.projection.unmount(),(0,k.WG)(this.notifyUpdate),(0,k.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=V.f.has(t),r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&k.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in a.B){let e=a.B[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,o.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<C.length;e++){let i=C[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if((0,v.S)(r))t.addValue(n,r);else if((0,v.S)(s))t.addValue(n,(0,f.OQ)(r,{owner:t}));else if(s!==r){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,(0,f.OQ)(void 0!==e?e:r,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,f.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let n=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&((0,l.i)(n)||(0,u.$)(n))?n=parseFloat(n):!A(n)&&m.f.test(e)&&(n=(0,y.J)(t,e)),this.setBaseTarget(t,(0,v.S)(n)?n.get():n)),(0,v.S)(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let r=(0,M.a)(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);r&&(i=r[t])}if(n&&void 0!==i)return i;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||(0,v.S)(r)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new c.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}var E=i(8216);class O extends F{constructor(){super(...arguments),this.KeyframeResolver=E.K}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,v.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}var I=i(12271),D=i(99204),N=i(67192),$=i(98535),R=i(701);class K extends O{constructor(){super(...arguments),this.type="html",this.renderInstance=$.e}readValueFromInstance(t,e){if(V.f.has(e)){let t=(0,D.D)(e);return t&&t.default||0}{let i=window.getComputedStyle(t),n=((0,I.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,r.m)(t,e)}build(t,e,i){(0,N.O)(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return(0,R.x)(t,e,i)}}var B=i(42717),q=i(71721),L=i(19552),U=i(7986),W=i(47928),G=i(72126);class X extends O{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=o.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(V.f.has(e)){let t=(0,D.D)(e);return t&&t.default||0}return e=L.e.has(e)?e:(0,B.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return(0,G.x)(t,e,i)}build(t,e,i){(0,q.B)(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,n){(0,W.d)(t,e,i,n)}mount(t){this.isSVGTag=(0,U.n)(t.tagName),super.mount(t)}}var j=i(75526);let H=(t,e)=>(0,j.Q)(t)?new X(e):new K(e,{allowProjection:t!==n.Fragment})},40403:(t,e,i)=>{i.d(e,{E4:()=>a,Hr:()=>p,W9:()=>d});var n=i(41535),r=i(43406),s=i(13480);let a=t=>t===n.ai||t===r.px,o=(t,e)=>parseFloat(t.split(", ")[e]),l=(t,e)=>(i,{transform:n})=>{if("none"===n||!n)return 0;let r=n.match(/^matrix3d\((.+)\)$/u);if(r)return o(r[1],e);{let e=n.match(/^matrix\((.+)\)$/u);return e?o(e[1],t):0}},u=new Set(["x","y","z"]),h=s.U.filter(t=>!u.has(t));function d(t){let e=[];return h.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e}let p={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:l(4,13),y:l(5,14)};p.translateX=p.x,p.translateY=p.y},1435:(t,e,i)=>{i.d(e,{J:()=>a});var n=i(55254),r=i(20139),s=i(99204);function a(t,e){let i=(0,s.D)(t);return i!==r.p&&(i=n.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}},99204:(t,e,i)=>{i.d(e,{D:()=>a});var n=i(94064),r=i(20139);let s={...i(618).W,color:n.y,backgroundColor:n.y,outlineColor:n.y,fill:n.y,stroke:n.y,borderColor:n.y,borderTopColor:n.y,borderRightColor:n.y,borderBottomColor:n.y,borderLeftColor:n.y,filter:r.p,WebkitFilter:r.p},a=t=>s[t]},18201:(t,e,i)=>{i.d(e,{T:()=>a,n:()=>o});var n=i(41535),r=i(43406),s=i(83510);let a=[n.ai,r.px,r.KN,r.uj,r.vw,r.vh,{test:t=>"auto"===t,parse:t=>t}],o=t=>a.find((0,s.w)(t))},83510:(t,e,i)=>{i.d(e,{w:()=>n});let n=t=>e=>e.test(t)},67287:(t,e,i)=>{i.d(e,{$:()=>n});let n=new Set(["width","height","top","left","right","bottom",...i(13480).U])},22368:(t,e,i)=>{i.d(e,{h:()=>d,q:()=>h});var n=i(40403),r=i(3307);let s=new Set,a=!1,o=!1;function l(){if(o){let t=Array.from(s).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=(0,n.W9)(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var n;null===(n=t.getValue(e))||void 0===n||n.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}o=!1,a=!1,s.forEach(t=>t.complete()),s.clear()}function u(){s.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(o=!0)})}function h(){u(),l()}class d{constructor(t,e,i,n,r,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(s.add(this),a||(a=!0,r.Gt.read(u),r.Gt.resolveKeyframes(l))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;for(let r=0;r<t.length;r++)if(null===t[r]){if(0===r){let r=null==n?void 0:n.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}else t[r]=t[r-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),s.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,s.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}},24093:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function r(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>r,Kq:()=>n})},20555:(t,e,i)=>{i.d(e,{i:()=>n});let n=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t)},66879:(t,e,i)=>{i.d(e,{$:()=>n});let n=t=>/^0[^.\s]+$/u.test(t)},50010:(t,e,i)=>{i.d(e,{k:()=>n});let n=(t,e,i)=>t+(e-t)*i},15068:(t,e,i)=>{i.d(e,{F:()=>r});let n=(t,e)=>i=>e(t(i)),r=(...t)=>t.reduce(n)},87945:(t,e,i)=>{i.d(e,{v:()=>r});var n=i(24093);class r{constructor(){this.subscriptions=[]}add(t){return(0,n.Kq)(this.subscriptions,t),()=>(0,n.Ai)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},42206:(t,e,i)=>{i.d(e,{f:()=>n});function n(t,e){return e?1e3/e*t:0}},93250:(t,e,i)=>{i.d(e,{OQ:()=>h});var n=i(79125),r=i(87945),s=i(42206),a=i(3307);let o=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=n.k.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=n.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new r.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=n.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,s.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},98967:(t,e,i)=>{i.d(e,{u:()=>r});var n=i(2664);let r={test:(0,i(60233).$)("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:n.B.transform}},83020:(t,e,i)=>{i.d(e,{V:()=>o});var n=i(41535),r=i(43406),s=i(76165),a=i(60233);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+r.KN.transform((0,s.a)(e))+", "+r.KN.transform((0,s.a)(i))+", "+(0,s.a)(n.X4.transform(a))+")"}},94064:(t,e,i)=>{i.d(e,{y:()=>a});var n=i(98967),r=i(83020),s=i(2664);let a={test:t=>s.B.test(t)||n.u.test(t)||r.V.test(t),parse:t=>s.B.test(t)?s.B.parse(t):r.V.test(t)?r.V.parse(t):n.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?s.B.transform(t):r.V.transform(t)}},2664:(t,e,i)=>{i.d(e,{B:()=>u});var n=i(16611),r=i(41535),s=i(76165),a=i(60233);let o=t=>(0,n.q)(0,255,t),l={...r.ai,transform:t=>Math.round(o(t))},u={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,s.a)(r.X4.transform(n))+")"}},60233:(t,e,i)=>{i.d(e,{$:()=>s,q:()=>a});var n=i(41014);let r=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(t,e)=>i=>!!("string"==typeof i&&r.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[s,a,o,l]=r.match(n.S);return{[t]:parseFloat(s),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},20139:(t,e,i)=>{i.d(e,{p:()=>l});var n=i(55254),r=i(41014);let s=new Set(["brightness","contrast","saturate","opacity"]);function a(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(r.S)||[];if(!n)return t;let a=i.replace(n,""),o=s.has(e)?1:0;return n!==i&&(o*=100),e+"("+o+a+")"}let o=/\b([a-z-]*)\(.*?\)/gu,l={...n.f,getAnimatableNone:t=>{let e=t.match(o);return e?e.map(a).join(" "):t}}},55254:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>f});var n=i(94064);let r=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=i(41014),a=i(76165);let o="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},s=[],a=0,h=e.replace(u,t=>(n.y.test(t)?(r.color.push(a),s.push(l),i.push(n.y.parse(t))):t.startsWith("var(")?(r.var.push(a),s.push("var"),i.push(t)):(r.number.push(a),s.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:h,indexes:r,types:s}}function d(t){return h(t).values}function p(t){let{split:e,types:i}=h(t),r=e.length;return t=>{let s="";for(let u=0;u<r;u++)if(s+=e[u],void 0!==t[u]){let e=i[u];e===o?s+=(0,a.a)(t[u]):e===l?s+=n.y.transform(t[u]):s+=t[u]}return s}}let c=t=>"number"==typeof t?0:t,f={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(s.S))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(r))||void 0===i?void 0:i.length)||0)>0},parse:d,createTransformer:p,getAnimatableNone:function(t){let e=d(t);return p(t)(e.map(c))}}},41014:(t,e,i)=>{i.d(e,{S:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},76165:(t,e,i)=>{i.d(e,{a:()=>n});let n=t=>Math.round(1e5*t)/1e5},86901:(t,e,i)=>{i.d(e,{g:()=>r});var n=i(84707);function r(t,e){let i=t.getValue("willChange");if((0,n.S)(i)&&i.add)return i.add(e)}},75713:(t,e,i)=>{i.d(e,{P6:()=>a,vG:()=>d,tu:()=>u,KZ:()=>v,rU:()=>o,PT:()=>V,DW:()=>p,WH:()=>h,Mc:()=>x,yL:()=>y,TU:()=>function t(e,i){if(e)return"function"==typeof e&&f()?v(e,i):p(e)?g(e):Array.isArray(e)?e.map(e=>t(e,i)||b.easeOut):b[e]},YE:()=>l,c$:()=>O,Wp:()=>I,nL:()=>f});var n=i(64996);let r=(0,n.p)(()=>void 0!==window.ScrollTimeline);class s{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>r()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class a extends s{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function o(t,e){return t?t[e]||t.default||t:void 0}let l=2e4;function u(t){let e=0,i=t.next(e);for(;!i.done&&e<l;)e+=50,i=t.next(e);return e>=l?1/0:e}function h(t){return"function"==typeof t}function d(t,e){t.timeline=e,t.onfinish=null}let p=t=>Array.isArray(t)&&"number"==typeof t[0],c={linearEasing:void 0},f=function(t,e){let i=(0,n.p)(t);return()=>{var t;return null!==(t=c[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var m=i(19615);let v=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t((0,m.q)(0,r-1,e))+", ";return`linear(${n.substring(0,n.length-2)})`};function y(t){return!!("function"==typeof t&&f()||!t||"string"==typeof t&&(t in b||f())||p(t)||Array.isArray(t)&&t.every(y))}let g=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,b={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:g([0,.65,.55,1]),circOut:g([.55,0,1,.45]),backIn:g([.31,.01,.66,-.59]),backOut:g([.33,1.53,.69,.99])},w={x:!1,y:!1};function S(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function A(t){return e=>{"touch"===e.pointerType||w.x||w.y||t(e)}}function V(t,e,i={}){let[n,r,s]=S(t,i),a=A(t=>{let{target:i}=t,n=e(t);if("function"!=typeof n||!i)return;let s=A(t=>{n(t),i.removeEventListener("pointerleave",s)});i.addEventListener("pointerleave",s,r)});return n.forEach(t=>{t.addEventListener("pointerenter",a,r)}),s}let T=(t,e)=>!!e&&(t===e||T(t,e.parentElement)),x=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,P=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),M=new WeakSet;function k(t){return e=>{"Enter"===e.key&&t(e)}}function C(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let F=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=k(()=>{if(M.has(i))return;C(i,"down");let t=k(()=>{C(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>C(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function E(t){return x(t)&&!(w.x||w.y)}function O(t,e,i={}){let[n,r,s]=S(t,i),a=t=>{let n=t.currentTarget;if(!E(t)||M.has(n))return;M.add(n);let s=e(t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),E(t)&&M.has(n)&&(M.delete(n),"function"==typeof s&&s(t,{success:e}))},o=t=>{a(t,i.useGlobalTarget||T(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{P.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",a,r),t.addEventListener("focus",t=>F(t,r),r)}),s}function I(t){return"x"===t||"y"===t?w[t]?null:(w[t]=!0,()=>{w[t]=!1}):w.x||w.y?null:(w.x=w.y=!0,()=>{w.x=w.y=!1})}i(40300),i(26054)},45107:(t,e,i)=>{i.d(e,{$:()=>r,V:()=>s});var n=i(26054);let r=n.l,s=n.l},64996:(t,e,i)=>{i.d(e,{p:()=>n});function n(t){let e;return()=>(void 0===e&&(e=t()),e)}},19615:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},40300:(t,e,i)=>{i.d(e,{X:()=>r,f:()=>n});let n=t=>1e3*t,r=t=>t/1e3}}]);