{"name": "palastine_emergcy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@nextui-org/button": "^2.2.8", "@nextui-org/image": "^2.0.32", "@nextui-org/input": "^2.2.5", "@nextui-org/link": "^2.2.6", "@nextui-org/react": "^2.4.8", "@nextui-org/tabs": "^2.0.37", "@vis.gl/react-maplibre": "^1.0.0-alpha.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.18.1", "lucide-react": "^0.473.0", "maplibre-gl": "^5.0.1", "next": "15.0.3", "react": "^19.0.0", "react-cookie": "^7.2.2", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.2", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}