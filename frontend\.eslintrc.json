{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-empty-object-type": "off", "react/no-unescaped-entities": "off", "@typescript-eslint/no-unsafe-function-type": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-expressions": "warn", "@typescript-eslint/no-unused-vars": "warn", "react-hooks/rules-of-hooks": "warn", "react-hooks/exhaustive-deps": "warn"}}