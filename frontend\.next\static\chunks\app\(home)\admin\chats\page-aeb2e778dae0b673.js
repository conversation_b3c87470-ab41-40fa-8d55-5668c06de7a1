(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[517],{90448:(e,s,i)=>{Promise.resolve().then(i.bind(i,84408))},84408:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>r,dynamic:()=>d});var n=i(95155);i(12115),i(30814);let d="force-dynamic";function r(){return(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{children:"<PERSON><PERSON>ts Page"}),(0,n.jsx)("p",{children:"This is a test page to debug the build issue."})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[814,441,898,358],()=>s(90448)),_N_E=e.O()}]);