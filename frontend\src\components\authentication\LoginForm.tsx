"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Eye, EyeOff } from "lucide-react";
import { loginSchema, LoginFormValues } from "@/schemas/auth";
import { Input, Button, cn } from "@nextui-org/react";
import { Link } from "@nextui-org/link";
import { useCookies } from "react-cookie";
import { fetcher } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export default function LoginForm() {
    const router = useRouter();
    const [showPassword, setShowPassword] = useState(false);
    const [cookies, setCookie, removeCookie] = useCookies();

    const {
        control,
        handleSubmit,
        formState: { errors, isSubmitting },
    } = useForm<LoginFormValues>({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: "",
            password: "",
        },
    });

    async function onSubmit(values: LoginFormValues) {
        try {
            console.log("Login attempt with:", { email: values.email, passwordLength: values.password?.length });

            // Ensure we're sending the right field names for Djoser
            const loginData = {
                email: values.email,
                password: values.password
            };

            const res = await fetcher("/auth/jwt/create/", loginData, "POST");
            console.log("Login response status:", res.status);

            if (res.status === 200) {
                const data = await res.json();
                console.log("Login successful, tokens received");

                if (data.access && data.refresh) {
                    setCookie("access", data.access);
                    setCookie("refresh", data.refresh);
                    toast.success("تم تسجيل الدخول بنجاح");
                    router.push("/");
                } else {
                    console.error("Missing tokens in response:", data);
                    toast.error("حدث خطأ في تسجيل الدخول، الرجاء المحاولة مرة أخرى");
                }
            } else {
                const errorData = await res.json().catch(() => ({}));
                console.error("Login error:", res.status, errorData);

                if (res.status === 400) {
                    toast.error("بيانات غير صحيحة، تأكد من البريد الإلكتروني وكلمة المرور");
                } else if (res.status === 401) {
                    toast.error("غير مصرح لك بالدخول، تأكد من بياناتك");
                } else {
                    toast.error("فشل تسجيل الدخول، الرجاء المحاولة مرة أخرى");
                }
            }
        } catch (error) {
            console.error("Login submission error:", error);
            toast.error("حدث خطأ أثناء محاولة تسجيل الدخول");
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 mt-6">
            <Controller
                name="email"
                control={control}
                render={({ field }) => (
                    <Input
                        {...field}
                        type="email"
                        label="البريد الإلكتروني"
                        variant="bordered"
                        isInvalid={!!errors.email}
                        errorMessage={errors.email?.message}
                    />
                )}
            />

            <Controller
                name="password"
                control={control}
                render={({ field }) => (
                    <Input
                        {...field}
                        type={showPassword ? "text" : "password"}
                        label="كلمة المرور"
                        variant="bordered"
                        isInvalid={!!errors.password}
                        errorMessage={errors.password?.message}
                        endContent={
                            <button
                                type="button"
                                className="h-full pl-2"
                                onClick={() => setShowPassword(!showPassword)}
                            >
                                {showPassword ? (
                                    <EyeOff size={20} />
                                ) : (
                                    <Eye size={20} />
                                )}
                            </button>
                        }
                    />
                )}
            />

            <div className="flex items-center justify-end">
                <Button
                    as={Link}
                    href="/auth/forget-password"
                    variant="light"
                    className="px-0 font-normal"
                >
                    هل نسيت كلمة المرور؟
                </Button>
            </div>

            <Button
                type="submit"
                color="primary"
                className={cn("w-full", isSubmitting ? "opacity-50" : "")}
                disabled={isSubmitting}
            >
                تسجيل الدخول
            </Button>
        </form>
    );
}
