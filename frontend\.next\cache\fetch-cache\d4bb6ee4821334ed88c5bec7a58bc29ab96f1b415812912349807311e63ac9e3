{"kind": "FETCH", "data": {"headers": {"allow": "GET, HEAD, OPTIONS", "content-length": "628", "content-type": "application/json", "cross-origin-opener-policy": "same-origin", "referrer-policy": "same-origin", "server": "daphne", "vary": "Accept, origin", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "eyJjb3VudCI6MSwibmV4dCI6bnVsbCwicHJldmlvdXMiOm51bGwsInJlc3VsdHMiOlt7ImlkIjoxLCJlbWVyZ2VuY3lfdHlwZSI6Ik0iLCJjcmVhdGVkX2F0IjoiMjAyNS0wNS0yM1QxNjoxMToyOS43OTU1MzlaIiwibG9jYXRpb24iOiLYsdin2YUg2KfZhNmE2YciLCJpbWFnZSI6Ii9tZWRpYS9lbWVyZ2VuY3kvaW1hZ2VzLyVEOSU4NSVEOCVBOCVEOCVBNyVEOCVCNCVEOCVCMV8lRDglQUQlRDglQTclRDglQUYlRDglQUJfJUQ5JTg1JUQ4JUIxJUQ5JTg4JUQ4JUIxXyVEOSU4MSVEOSU4QV8lRDklODclRDglQjAlRDklODdfJUQ4JUE3JUQ5JTg0JUQ4JUE3JUQ4JUFCJUQ5JTg2JUQ4JUE3JUQ4JUExXyVEOCVBOCVEOSU4NSVEOSU4NiVEOCVCNyVEOSU4MiVEOCVBOV8lRDglQTclRDklODUlRDglQjclRDklOEElRDklODJfJUQ4JUE3JUQ4JUIyJUQ4JUE4JUQ5JTg4JUQ4JUI0XyVEOCVBOCVEOSU4QSVEOSU4Nl8lRDglQUYlRDglQjElRDklODIlRDklOEElRDklODYlRDglQTlfJUQ5JTg1JUQ4JUIxJUQ5JTgzJUQ4JUIyXyVEOSU4OCVEOSU4MiVEOCVCMSVEOSU4QSVEOCVBOV9HRjQ0OWUxLmpwZyIsInVzZXJfZmlyc3RfbmFtZSI6Ik1vbnRhc2VyQmFsbGgiLCJ1c2VyX2xhc3RfbmFtZSI6IkhhcmZvdXNoIn1dfQ==", "status": 200, "url": "http://localhost:8000/emergency/?emergency_type=M"}, "revalidate": 31536000, "tags": []}