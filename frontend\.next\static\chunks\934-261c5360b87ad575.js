"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[934],{86934:(e,t,o)=>{o.d(t,{lT:()=>g});var r,i,n,s,a={},c=function(){if(r)return a;r=1,a.parse=function(e,o){if("string"!=typeof e)throw TypeError("argument str must be a string");var r={},i=e.length;if(i<2)return r;var n=o&&o.decode||f,s=0,a=0,l=0;do{if(-1===(a=e.indexOf("=",s)))break;if(-1===(l=e.indexOf(";",s)))l=i;else if(a>l){s=e.lastIndexOf(";",a-1)+1;continue}var p=c(e,s,a),d=u(e,a,p),h=e.slice(p,d);if(!t.call(r,h)){var y=c(e,a+1,l),m=u(e,l,y);34===e.charCodeAt(y)&&34===e.charCodeAt(m-1)&&(y++,m--);var g=e.slice(y,m);r[h]=function(e,t){try{return t(e)}catch(t){return e}}(g,n)}s=l+1}while(s<i);return r},a.serialize=function(t,r,a){var c=a&&a.encode||encodeURIComponent;if("function"!=typeof c)throw TypeError("option encode is invalid");if(!o.test(t))throw TypeError("argument name is invalid");var u=c(r);if(!i.test(u))throw TypeError("argument val is invalid");var f=t+"="+u;if(!a)return f;if(null!=a.maxAge){var l=Math.floor(a.maxAge);if(!isFinite(l))throw TypeError("option maxAge is invalid");f+="; Max-Age="+l}if(a.domain){if(!n.test(a.domain))throw TypeError("option domain is invalid");f+="; Domain="+a.domain}if(a.path){if(!s.test(a.path))throw TypeError("option path is invalid");f+="; Path="+a.path}if(a.expires){var p=a.expires;if("[object Date]"!==e.call(p)||isNaN(p.valueOf()))throw TypeError("option expires is invalid");f+="; Expires="+p.toUTCString()}if(a.httpOnly&&(f+="; HttpOnly"),a.secure&&(f+="; Secure"),a.partitioned&&(f+="; Partitioned"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():a.priority){case"low":f+="; Priority=Low";break;case"medium":f+="; Priority=Medium";break;case"high":f+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":f+="; SameSite=Strict";break;case"lax":f+="; SameSite=Lax";break;case"none":f+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return f};var e=Object.prototype.toString,t=Object.prototype.hasOwnProperty,o=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/,i=/^("?)[\u0021\u0023-\u002B\u002D-\u003A\u003C-\u005B\u005D-\u007E]*\1$/,n=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/;function c(e,t,o){do{var r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<o);return o}function u(e,t,o){for(;t>o;){var r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return o}function f(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}return a}();function u(e,t={}){let o=e&&"j"===e[0]&&":"===e[1]?e.substr(2):e;if(!t.doNotParse)try{return JSON.parse(o)}catch(e){}return e}class f{constructor(e,t={}){this.changeListeners=[],this.HAS_DOCUMENT_COOKIE=!1,this.update=()=>{if(!this.HAS_DOCUMENT_COOKIE)return;let e=this.cookies;this.cookies=c.parse(document.cookie),this._checkChanges(e)};let o="undefined"==typeof document?"":document.cookie;this.cookies=function(e){return"string"==typeof e?c.parse(e):"object"==typeof e&&null!==e?e:{}}(e||o),this.defaultSetOptions=t,this.HAS_DOCUMENT_COOKIE=function(){let e="undefined"==typeof global?void 0:global.TEST_HAS_DOCUMENT_COOKIE;return"boolean"==typeof e?e:"object"==typeof document&&"string"==typeof document.cookie}()}_emitChange(e){for(let t=0;t<this.changeListeners.length;++t)this.changeListeners[t](e)}_checkChanges(e){new Set(Object.keys(e).concat(Object.keys(this.cookies))).forEach(t=>{e[t]!==this.cookies[t]&&this._emitChange({name:t,value:u(this.cookies[t])})})}_startPolling(){this.pollingInterval=setInterval(this.update,300)}_stopPolling(){this.pollingInterval&&clearInterval(this.pollingInterval)}get(e,t={}){return t.doNotUpdate||this.update(),u(this.cookies[e],t)}getAll(e={}){e.doNotUpdate||this.update();let t={};for(let o in this.cookies)t[o]=u(this.cookies[o],e);return t}set(e,t,o){o=o?Object.assign(Object.assign({},this.defaultSetOptions),o):this.defaultSetOptions;let r="string"==typeof t?t:JSON.stringify(t);this.cookies=Object.assign(Object.assign({},this.cookies),{[e]:r}),this.HAS_DOCUMENT_COOKIE&&(document.cookie=c.serialize(e,r,o)),this._emitChange({name:e,value:t,options:o})}remove(e,t){let o=t=Object.assign(Object.assign(Object.assign({},this.defaultSetOptions),t),{expires:new Date(1970,1,1,0,0,1),maxAge:0});this.cookies=Object.assign({},this.cookies),delete this.cookies[e],this.HAS_DOCUMENT_COOKIE&&(document.cookie=c.serialize(e,"",o)),this._emitChange({name:e,value:void 0,options:t})}addChangeListener(e){this.changeListeners.push(e),this.HAS_DOCUMENT_COOKIE&&1===this.changeListeners.length&&("object"==typeof window&&"cookieStore"in window?window.cookieStore.addEventListener("change",this.update):this._startPolling())}removeChangeListener(e){let t=this.changeListeners.indexOf(e);t>=0&&this.changeListeners.splice(t,1),this.HAS_DOCUMENT_COOKIE&&0===this.changeListeners.length&&("object"==typeof window&&"cookieStore"in window?window.cookieStore.removeEventListener("change",this.update):this._stopPolling())}}var l=o(12115);let p=l.createContext(new f),{Provider:d,Consumer:h}=p;"function"==typeof SuppressedError&&SuppressedError;var y={exports:{}},m={};function g(e,t){let o=(0,l.useContext)(p);if(!o)throw Error("Missing <CookiesProvider>");let r=Object.assign(Object.assign({},{doNotUpdate:!0}),t),[i,n]=(0,l.useState)(()=>o.getAll(r));return"undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement&&(0,l.useLayoutEffect)(()=>{function t(){let t=o.getAll(r);(function(e,t,o){if(!e)return!0;for(let r of e)if(t[r]!==o[r])return!0;return!1})(e||null,t,i)&&n(t)}return o.addChangeListener(t),()=>{o.removeChangeListener(t)}},[o,i]),[i,(0,l.useMemo)(()=>o.set.bind(o),[o]),(0,l.useMemo)(()=>o.remove.bind(o),[o]),(0,l.useMemo)(()=>o.update.bind(o),[o])]}!function(){if(!s){s=1;var e=(n||(n=1,y.exports=function(){if(i)return m;i=1;var e="function"==typeof Symbol&&Symbol.for,t=e?Symbol.for("react.element"):60103,o=e?Symbol.for("react.portal"):60106,r=e?Symbol.for("react.fragment"):60107,n=e?Symbol.for("react.strict_mode"):60108,s=e?Symbol.for("react.profiler"):60114,a=e?Symbol.for("react.provider"):60109,c=e?Symbol.for("react.context"):60110,u=e?Symbol.for("react.async_mode"):60111,f=e?Symbol.for("react.concurrent_mode"):60111,l=e?Symbol.for("react.forward_ref"):60112,p=e?Symbol.for("react.suspense"):60113,d=e?Symbol.for("react.suspense_list"):60120,h=e?Symbol.for("react.memo"):60115,y=e?Symbol.for("react.lazy"):60116,g=e?Symbol.for("react.block"):60121,S=e?Symbol.for("react.fundamental"):60117,b=e?Symbol.for("react.responder"):60118,v=e?Symbol.for("react.scope"):60119;function O(e){if("object"==typeof e&&null!==e){var i=e.$$typeof;switch(i){case t:switch(e=e.type){case u:case f:case r:case s:case n:case p:return e;default:switch(e=e&&e.$$typeof){case c:case l:case y:case h:case a:return e;default:return i}}case o:return i}}}function w(e){return O(e)===f}return m.AsyncMode=u,m.ConcurrentMode=f,m.ContextConsumer=c,m.ContextProvider=a,m.Element=t,m.ForwardRef=l,m.Fragment=r,m.Lazy=y,m.Memo=h,m.Portal=o,m.Profiler=s,m.StrictMode=n,m.Suspense=p,m.isAsyncMode=function(e){return w(e)||O(e)===u},m.isConcurrentMode=w,m.isContextConsumer=function(e){return O(e)===c},m.isContextProvider=function(e){return O(e)===a},m.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===t},m.isForwardRef=function(e){return O(e)===l},m.isFragment=function(e){return O(e)===r},m.isLazy=function(e){return O(e)===y},m.isMemo=function(e){return O(e)===h},m.isPortal=function(e){return O(e)===o},m.isProfiler=function(e){return O(e)===s},m.isStrictMode=function(e){return O(e)===n},m.isSuspense=function(e){return O(e)===p},m.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===f||e===s||e===n||e===p||e===d||"object"==typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===h||e.$$typeof===a||e.$$typeof===c||e.$$typeof===l||e.$$typeof===S||e.$$typeof===b||e.$$typeof===v||e.$$typeof===g)},m.typeOf=O,m}()),y.exports),t={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},r={};r[e.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},r[e.Memo]=o}}()}}]);