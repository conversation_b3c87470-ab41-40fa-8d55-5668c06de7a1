(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[393],{60751:(e,s,a)=>{Promise.resolve().then(a.bind(a,90513))},90513:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>C,dynamic:()=>w});var r=a(95155),l=a(21567),t=a(85060);let o=(0,a(14057).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var n=a(29609),d=a(76046),i=a(12115),c=a(86934),u=a(69606),b=a(1063),m=a(17281),h=a(80117),x=a(5582),f=a(83517),v=a(49163),g=(0,x.Rf)((e,s)=>{var a;let{as:l,className:t,children:o,...n}=e,d=(0,f.zD)(s),{slots:i,classNames:c}=(0,h.f)(),u=(0,v.$)(null==c?void 0:c.header,t);return(0,r.jsx)(l||"div",{ref:d,className:null==(a=i.header)?void 0:a.call(i,{class:u}),...n,children:o})});g.displayName="NextUI.CardHeader";var p=a(77803),y=a(62113),j=a(94936),N=a(59451);let w="force-dynamic";function k(){var e;let[s]=(0,c.lT)(["access"]),a=(0,d.useParams)(),h=(0,d.useSearchParams)(),x=(0,d.useRouter)(),f=a.id,v=h.get("room"),w=(0,i.useRef)(null),[k,C]=(0,i.useState)([]),[S,P]=(0,i.useState)(!1),E=(0,i.useRef)(null),D=s.access,H=(0,i.useContext)(N.UserContext),R=null==H?void 0:null===(e=H.user)||void 0===e?void 0:e.id,{control:W,handleSubmit:_,formState:{errors:B,isSubmitting:I},reset:O}=(0,u.mN)({resolver:(0,t.u)(b.y),defaultValues:{message:""}}),z=()=>{var e;null===(e=w.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};return((0,i.useEffect)(()=>{if(f&&v&&D&&v){(0,l.G)("/chats/".concat(f,"/messages"),null,"GET",D).then(e=>e.json()).then(e=>{C(e.results)}).catch(e=>{console.error("Error fetching room info:",e)});let e=new WebSocket("ws://localhost:8000/ws/chat/".concat(v,"/?token=").concat(D));return E.current=e,e.onopen=()=>{console.log("WebSocket connected"),P(!0)},e.onmessage=e=>{let s;try{s=JSON.parse(e.data)}catch(s){console.error("Received non-JSON message:",e.data);return}console.log(s),C(e=>[{id:Math.random(),sender:s.user,body:s.message,chat_room:v,created_at:new Date().toISOString()},...e])},e.onclose=()=>{console.log("WebSocket disconnected"),P(!1)},()=>{e.close()}}},[D,v,f]),(0,i.useEffect)(()=>{z()},[k]),f&&v)?(0,r.jsx)("div",{className:"container mx-auto py-6 max-w-4xl",children:(0,r.jsxs)(m.Z,{className:"border shadow-md h-[calc(100vh-100px)] flex flex-col",children:[(0,r.jsx)(g,{className:"bg-gray-50 dark:bg-gray-800 border-b px-4 py-3 flex flex-row items-center justify-between space-y-0",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(j.T,{onClick:()=>x.push("/admin/chats"),className:"mr-2",children:(0,r.jsx)(o,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg",children:"اسم المستخدم"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["رقم الغرفة: ",v]})})]})]})}),(0,r.jsx)(p.U,{className:"flex-1 fle overflow-y-auto p-4 space-y-4",children:0===k.length?(0,r.jsx)("div",{className:"grid place-content-center h-full",children:(0,r.jsx)("p",{className:"text-gray-500",children:"لا يوجد رسائل بعد"})}):(0,r.jsxs)("div",{className:"flex flex-col-reverse gap-3",children:[k.map(e=>(0,r.jsx)("div",{className:"flex ".concat(e.sender===R?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"max-w-[70%] px-4 py-2 rounded-lg ".concat(e.sender===R?"bg-blue-600 text-white rounded-br-none":"bg-gray-100 dark:bg-gray-800 rounded-bl-none"),children:[(0,r.jsx)("p",{className:"text-sm",children:e.body}),(0,r.jsx)("span",{className:"text-xs mt-1 block text-right ".concat(e.sender===R?"text-blue-100":"text-gray-500"),children:(0,l.f)(e.created_at)})]})},e.id)),(0,r.jsx)("div",{ref:w})]})}),(0,r.jsxs)("div",{className:"p-4 border-t",children:[(0,r.jsxs)("form",{className:"flex gap-2",onSubmit:_(e=>{var s;(null===(s=E.current)||void 0===s?void 0:s.readyState)===WebSocket.OPEN&&(E.current.send(JSON.stringify({message:e.message})),O())}),children:[(0,r.jsx)(u.xI,{name:"message",control:W,render:e=>{let{field:s}=e;return(0,r.jsx)(y.r,{...s,placeholder:"قم بكتابة رسالتك هنا...",className:"flex-1",disabled:!S})}}),(0,r.jsxs)(j.T,{type:"submit",disabled:I||!S,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"ارسال"]})]}),B.message&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:B.message.message})]})]})}):(0,d.notFound)()}function C(){return(0,r.jsx)(i.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(k,{})})}},17281:(e,s,a)=>{"use strict";a.d(s,{Z:()=>k});var r=a(80117),l=a(1206),t=a(2877),o=(0,l.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...t.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),n=a(12115),d=a(67093),i=a(97262),c=a(57010),u=a(13201),b=a(35130),m=a(84725),h=a(5582),x=a(49163),f=a(55078),v=a(46611),g=a(33138),p=a(83517),y=a(65858),j=a(39867),N=a(95155),w=(0,h.Rf)((e,s)=>{let{children:a,context:l,Component:t,isPressable:w,disableAnimation:k,disableRipple:C,getCardProps:S,getRippleProps:P}=function(e){var s,a,r,l;let t=(0,m.o)(),[j,N]=(0,h.rE)(e,o.variantKeys),{ref:w,as:k,children:C,onClick:S,onPress:P,autoFocus:E,className:D,classNames:H,allowTextSelectionOnPress:R=!0,...W}=j,_=(0,p.zD)(w),B=k||(e.isPressable?"button":"div"),I="string"==typeof B,O=null!=(a=null!=(s=e.disableAnimation)?s:null==t?void 0:t.disableAnimation)&&a,z=null!=(l=null!=(r=e.disableRipple)?r:null==t?void 0:t.disableRipple)&&l,A=(0,x.$)(null==H?void 0:H.base,D),{onClear:F,onPress:M,ripples:U}=(0,y.k)(),T=(0,n.useCallback)(e=>{z||O||!_.current||M(e)},[z,O,_,M]),{buttonProps:$,isPressed:V}=(0,b.l)({onPress:(0,d.c)(P,T),elementType:k,isDisabled:!e.isPressable,onClick:S,allowTextSelectionOnPress:R,...W},_),{hoverProps:J,isHovered:G}=(0,u.M)({isDisabled:!e.isHoverable,...W}),{isFocusVisible:L,isFocused:Z,focusProps:q}=(0,c.o)({autoFocus:E}),K=(0,n.useMemo)(()=>o({...N,disableAnimation:O}),[(0,f.t6)(N),O]),Q=(0,n.useMemo)(()=>({slots:K,classNames:H,disableAnimation:O,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[K,H,e.isDisabled,e.isFooterBlurred,O,e.fullWidth]),X=(0,n.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:_,className:K.base({class:A}),tabIndex:e.isPressable?0:-1,"data-hover":(0,v.sE)(G),"data-pressed":(0,v.sE)(V),"data-focus":(0,v.sE)(Z),"data-focus-visible":(0,v.sE)(L),"data-disabled":(0,v.sE)(e.isDisabled),...(0,i.v)(e.isPressable?{...$,...q,role:"button"}:{},e.isHoverable?J:{},(0,g.$)(W,{enabled:I}),(0,g.$)(s))}},[_,K,A,I,e.isPressable,e.isHoverable,e.isDisabled,G,V,L,$,q,J,W]),Y=(0,n.useCallback)(()=>({ripples:U,onClear:F}),[U,F]);return{context:Q,domRef:_,Component:B,classNames:H,children:C,isHovered:G,isPressed:V,disableAnimation:O,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:z,handlePress:T,isFocusVisible:L,getCardProps:X,getRippleProps:Y}}({...e,ref:s});return(0,N.jsxs)(t,{...S(),children:[(0,N.jsx)(r.u,{value:l,children:a}),w&&!k&&!C&&(0,N.jsx)(j.j,{...P()})]})});w.displayName="NextUI.Card";var k=w},77803:(e,s,a)=>{"use strict";a.d(s,{U:()=>i});var r=a(80117),l=a(5582),t=a(83517),o=a(49163),n=a(95155),d=(0,l.Rf)((e,s)=>{var a;let{as:l,className:d,children:i,...c}=e,u=(0,t.zD)(s),{slots:b,classNames:m}=(0,r.f)(),h=(0,o.$)(null==m?void 0:m.body,d);return(0,n.jsx)(l||"div",{ref:u,className:null==(a=b.body)?void 0:a.call(b,{class:h}),...c,children:i})});d.displayName="NextUI.CardBody";var i=d},80117:(e,s,a)=>{"use strict";a.d(s,{f:()=>l,u:()=>r});var[r,l]=(0,a(89583).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})}},e=>{var s=s=>e(e.s=s);e.O(0,[814,13,936,746,573,934,396,125,451,441,898,358],()=>s(60751)),_N_E=e.O()}]);