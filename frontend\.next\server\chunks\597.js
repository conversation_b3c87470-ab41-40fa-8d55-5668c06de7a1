exports.id=597,exports.ids=[597],exports.modules={59423:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,96313,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},96375:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,86013,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},79865:(e,t,s)=>{Promise.resolve().then(s.bind(s,56814)),Promise.resolve().then(s.bind(s,85764))},50489:(e,t,s)=>{Promise.resolve().then(s.bind(s,91542)),Promise.resolve().then(s.bind(s,69432))},99649:(e,t,s)=>{Promise.resolve().then(s.bind(s,36953))},12801:(e,t,s)=>{Promise.resolve().then(s.bind(s,70330))},70330:(e,t,s)=>{"use strict";s.r(t),s.d(t,{UserContext:()=>T,default:()=>E,dynamic:()=>G});var r=s(45512);let n=[{link:"/",text:"الصفحة الرئيسية"},{link:"/#send-emergency",text:"اشعار طوارئ"},{link:"/#recieved-emergency",text:"الاشعارات المستلمه"},{link:"/#call-us",text:"اتصل بنا"}];var a=s(46131),l=s(73456),o=s(67961),i=s(39146),c=s(34467),d=s(26008);function m(){let e=[{icon:a.A,href:"#"},{icon:l.A,href:"#"},{icon:o.A,href:"#"},{icon:i.A,href:"#"},{icon:c.A,href:"#"}];return(0,r.jsx)("footer",{className:"bg-gray-100",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"gap-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-blue-600",children:"نداء الوطن"}),(0,r.jsx)("p",{className:"text-gray-600",children:"صوتك في الطوارئ"})]}),(0,r.jsx)("div",{className:"h-[3rem] w-px bg-primary-700 hidden xl:block"}),(0,r.jsxs)("div",{className:"text-center mb-8 max-w-3xl mx-auto text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:['منصة "',(0,r.jsx)("span",{className:"font-bold text-primary-400",children:"نداء الوطن"}),'" هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن مخاطر تهددهم']}),"sp"]}),(0,r.jsx)("div",{className:"h-[3rem] w-px bg-primary-700 hidden xl:block"}),(0,r.jsxs)("div",{className:"grid gap-y-2",children:[(0,r.jsx)("p",{children:"وسائل التواصل الاجتماعي "}),(0,r.jsx)("div",{className:"flex gap-4",children:e.map((e,t)=>{let s=e.icon;return(0,r.jsx)(d.default,{href:e.href,className:"w-8 h-8 flex items-center justify-center rounded-full bg-white text-blue-500 hover:bg-blue-50 transition-colors",children:(0,r.jsx)(s,{className:"w-4 h-4"})},t)})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center pt-6 border-t border-gray-200",children:[(0,r.jsx)("nav",{children:(0,r.jsx)("ul",{className:"flex flex-wrap justify-center gap-4",children:n.map((e,t)=>(0,r.jsx)("li",{children:(0,r.jsx)(d.default,{href:e.link,className:"text-sm text-gray-600 hover:text-blue-500",children:e.text})},t))})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4 md:mb-0",children:"جميع حقوق النشر محفوظة لدى نداء الوطن \xa9 2025"})]})]})})}var x=s(84195),h=s(33543),p=s(58009),u=s(45900);function f(e){let[t,s,a]=(0,u.lT)(),l=(0,p.useContext)(T),o=l?.user;return(0,r.jsxs)("header",{className:"sticky m-8 px-6 py-4 flex justify-between backdrop-blur-sm rounded-xl z-[999]",children:[(0,r.jsx)(h.h,{href:"/",children:(0,r.jsx)("h1",{className:"font-extrabold",children:"نداء الوطن"})}),(0,r.jsxs)("div",{className:"flex gap-3",children:[n.map(({link:e,text:t})=>(0,r.jsx)(h.h,{href:e,className:"text-black hover:text-black/75",children:t},e)),o?.is_admin&&(0,r.jsx)(h.h,{href:"/admin/chats",className:"text-black hover:text-black/75",children:"رسائل العملاء"})]}),l?.isLoading?(0,r.jsx)("p",{children:"جارى التحميل"}):o&&l.isValid?(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("p",{className:"mt-3",children:["مرحبا ",o.first_name+" "+o.last_name]}),(0,r.jsx)(x.T,{as:h.h,href:"/",variant:"bordered",color:"danger",onPress:()=>{a("access"),a("refresh")},children:"تسجيل الخروج"})]}):(0,r.jsx)(x.T,{as:h.h,href:"/auth",variant:"bordered",color:"primary",children:"تسجيل الدخول"})]})}var b=s(44195),v=s(8137),j=s(81914),g=s(5637),y=s(47112),N=s(17397),w=s(96211),P=s(13289),C=s(6868);function k(){let[e]=(0,u.lT)(["access","user_id"]),t=(0,p.useRef)(null),[s,n]=(0,p.useState)(!1),[a,l]=(0,p.useState)([]),o=(0,p.useRef)(null);e.access;let{user:i}=(0,p.useContext)(T),c=i?.id||1,{control:d,handleSubmit:m,formState:{errors:h,isSubmitting:f},reset:P}=(0,C.mN)({resolver:(0,j.u)(v.y),defaultValues:{message:""}});return s?(0,r.jsxs)("div",{className:"h-[500px] w-[400px] bg-slate-200 fixed bottom-0 right-0 rounded-xl overflow-clip flex flex-col z-50 shadow-lg",children:[(0,r.jsxs)("header",{className:"flex justify-between items-center bg-blue-600 text-white py-3 px-2",children:[(0,r.jsx)("h2",{className:"font-semibold text-lg",children:"خدمة العملاء"}),(0,r.jsx)(x.T,{className:"bg-black/75 p-1 rounded-full min-w-8",onPress:()=>n(!1),children:(0,r.jsx)(y.A,{className:"text-white size-4"})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto px-2 py-3",children:0===a.length?(0,r.jsx)("div",{className:"grid place-content-center h-full",children:(0,r.jsx)("p",{children:"لا يوجد لديك رسائل"})}):(0,r.jsxs)("div",{className:"flex flex-col-reverse gap-2",children:[a.map(e=>(0,r.jsxs)("div",{className:(0,b.cn)("flex flex-col mb-1",e.sender===c?"items-end":"items-start"),children:[(0,r.jsx)("p",{className:(0,b.cn)("max-w-[250px] px-4 py-2 rounded-xl text-white text-sm",e.sender===c?"bg-red-500 rounded-tl-none":"bg-black/25 rounded-tr-none"),children:e.body}),(0,r.jsx)("span",{className:"text-xs mt-1 block text-right text-gray-500",children:(0,b.f)(e.created_at)})]},e.id)),(0,r.jsx)("div",{ref:t})]})}),(0,r.jsxs)("form",{className:"flex gap-2 px-2 border-t border-gray-300 pt-3 pb-2",onSubmit:m(e=>{o.current?.readyState===WebSocket.OPEN&&(o.current.send(JSON.stringify({message:e.message})),P())}),children:[(0,r.jsx)(C.xI,{name:"message",control:d,render:({field:e})=>(0,r.jsx)(g.r,{...e,placeholder:"أدخل رسالتك",isInvalid:!!h.message,errorMessage:h.message?.message})}),(0,r.jsx)(x.T,{type:"submit",disabled:f,className:"min-w-8 bg-blue-600",children:(0,r.jsx)(N.A,{className:"text-white"})})]})]}):(0,r.jsx)(x.T,{onPress:()=>n(!0),className:"fixed bottom-4 right-4 w-16 h-16 px-0 bg-blue-600 text-white rounded-full text-xl font-bold grid place-content-center z-40 shadow-lg",children:(0,r.jsx)(w.A,{className:"size-8"})})}let A=(0,P.default)(()=>Promise.resolve(k),{ssr:!1});var S=s(79334);s(91542);let G="force-dynamic",T=(0,p.createContext)(null);function I({children:e}){let[t,s]=(0,p.useState)(!0),[n,a]=(0,p.useState)(!1),[l,o]=(0,p.useState)(null),[i]=(0,u.lT)(),c=(0,S.useSearchParams)();return c?.get("error"),(0,r.jsx)(T.Provider,{value:{isValid:n,isLoading:t,user:l},children:(0,r.jsxs)("div",{className:"flex flex-col min-h-screen relative",children:[(0,r.jsx)(f,{}),(0,r.jsx)("main",{className:"flex-1 ",children:e}),(0,r.jsx)(m,{}),l&&!l?.is_admin&&(0,r.jsx)(A,{})]})})}function E({children:e}){return(0,r.jsx)(p.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(I,{children:e})})}},69432:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(45512),n=s(15952);function a({children:e}){return(0,r.jsx)(n.b,{children:e})}s(58009)},44195:(e,t,s)=>{"use strict";s.d(t,{G:()=>l,cn:()=>a,f:()=>o});var r=s(82281),n=s(94805);function a(...e){return(0,n.QP)((0,r.$)(e))}async function l(e,t,s="GET",r){try{let n=await fetch("http://localhost:8000"+e,{method:s,body:null===t?null:JSON.stringify(t),headers:{"Content-Type":"application/json",Authorization:r?`Bearer ${r}`:""}});if(!n.ok)return console.error(`API error: ${n.status} ${n.statusText}`),{json:()=>Promise.resolve({error:!0,status:n.status,message:`API error: ${n.status} ${n.statusText}`,results:[]})};return n}catch(e){return console.error("Fetch error:",e),{json:()=>Promise.resolve({error:!0,message:e instanceof Error?e.message:"Unknown error",results:[]})}}}let o=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},8137:(e,t,s)=>{"use strict";s.d(t,{y:()=>n});var r=s(96314);let n=r.Ik({message:r.Yj().min(1,"يرجى إدخال رسالة")})},36953:(e,t,s)=>{"use strict";s.r(t),s.d(t,{UserContext:()=>a,default:()=>l,dynamic:()=>n});var r=s(46760);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\layout.tsx","dynamic"),a=(0,r.registerClientReference)(function(){throw Error("Attempted to call UserContext() from the server but UserContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\layout.tsx","UserContext"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (3)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\layout.tsx","default")},71354:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>i});var r=s(62740),n=s(98856),a=s.n(n);s(61135);var l=s(85764),o=s(56814);let i={title:"Palastine Emergency",description:"Comming for help when you need us"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{suppressHydrationWarning:!0,className:`${a().variable} antialiased`,children:[(0,r.jsx)(l.default,{children:e}),(0,r.jsx)(o.Toaster,{richColors:!0,position:"top-right"})]})})}},85764:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (3)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\components\\global\\ClientProviders.tsx","default")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(88077);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};