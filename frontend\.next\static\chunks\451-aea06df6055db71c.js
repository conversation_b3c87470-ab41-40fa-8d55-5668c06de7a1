"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[451],{59451:(e,s,t)=>{t.r(s),t.d(s,{UserContext:()=>C,default:()=>I,dynamic:()=>E});var r=t(95155);let l=[{link:"/",text:"الصفحة الرئيسية"},{link:"/#send-emergency",text:"اشعار طوارئ"},{link:"/#recieved-emergency",text:"الاشعارات المستلمه"},{link:"/#call-us",text:"اتصل بنا"}];var a=t(96799),n=t(34318),c=t(2345),i=t(60252),o=t(51085),d=t(67396);function x(){let e=[{icon:a.A,href:"#"},{icon:n.A,href:"#"},{icon:c.A,href:"#"},{icon:i.A,href:"#"},{icon:o.A,href:"#"}];return(0,r.jsx)("footer",{className:"bg-gray-100",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"gap-4",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-blue-600",children:"نداء الوطن"}),(0,r.jsx)("p",{className:"text-gray-600",children:"صوتك في الطوارئ"})]}),(0,r.jsx)("div",{className:"h-[3rem] w-px bg-primary-700 hidden xl:block"}),(0,r.jsxs)("div",{className:"text-center mb-8 max-w-3xl mx-auto text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:['منصة "',(0,r.jsx)("span",{className:"font-bold text-primary-400",children:"نداء الوطن"}),'" هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن مخاطر تهددهم']}),"sp"]}),(0,r.jsx)("div",{className:"h-[3rem] w-px bg-primary-700 hidden xl:block"}),(0,r.jsxs)("div",{className:"grid gap-y-2",children:[(0,r.jsx)("p",{children:"وسائل التواصل الاجتماعي "}),(0,r.jsx)("div",{className:"flex gap-4",children:e.map((e,s)=>{let t=e.icon;return(0,r.jsx)(d.default,{href:e.href,className:"w-8 h-8 flex items-center justify-center rounded-full bg-white text-blue-500 hover:bg-blue-50 transition-colors",children:(0,r.jsx)(t,{className:"w-4 h-4"})},s)})})]})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center pt-6 border-t border-gray-200",children:[(0,r.jsx)("nav",{children:(0,r.jsx)("ul",{className:"flex flex-wrap justify-center gap-4",children:l.map((e,s)=>(0,r.jsx)("li",{children:(0,r.jsx)(d.default,{href:e.link,className:"text-sm text-gray-600 hover:text-blue-500",children:e.text})},s))})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4 md:mb-0",children:"جميع حقوق النشر محفوظة لدى نداء الوطن \xa9 2025"})]})]})})}var h=t(94936),m=t(66532),u=t(12115),f=t(86934);function g(e){let[s,t,a]=(0,f.lT)(),n=(0,u.useContext)(C),c=null==n?void 0:n.user;return(0,r.jsxs)("header",{className:"sticky m-8 px-6 py-4 flex justify-between backdrop-blur-sm rounded-xl z-[999]",children:[(0,r.jsx)(m.h,{href:"/",children:(0,r.jsx)("h1",{className:"font-extrabold",children:"نداء الوطن"})}),(0,r.jsxs)("div",{className:"flex gap-3",children:[l.map(e=>{let{link:s,text:t}=e;return(0,r.jsx)(m.h,{href:s,className:"text-black hover:text-black/75",children:t},s)}),(null==c?void 0:c.is_admin)&&(0,r.jsx)(m.h,{href:"/admin/chats",className:"text-black hover:text-black/75",children:"رسائل العملاء"})]}),(null==n?void 0:n.isLoading)?(0,r.jsx)("p",{children:"جارى التحميل"}):c&&n.isValid?(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("p",{className:"mt-3",children:["مرحبا ",c.first_name+" "+c.last_name]}),(0,r.jsx)(h.T,{as:m.h,href:"/",variant:"bordered",color:"danger",onPress:()=>{a("access"),a("refresh")},children:"تسجيل الخروج"})]}):(0,r.jsx)(h.T,{as:m.h,href:"/auth",variant:"bordered",color:"primary",children:"تسجيل الدخول"})]})}var j=t(21567),b=t(1063),p=t(85060),v=t(62113),N=t(20652),y=t(29609),w=t(33325),k=t(77711),S=t(69606);function T(){let[e]=(0,f.lT)(["access","user_id"]),s=(0,u.useRef)(null),[t,l]=(0,u.useState)(!1),[a,n]=(0,u.useState)([]),c=(0,u.useRef)(null),i=e.access,{user:o}=(0,u.useContext)(C),d=(null==o?void 0:o.id)||1,{control:x,handleSubmit:m,formState:{errors:g,isSubmitting:k},reset:T}=(0,S.mN)({resolver:(0,p.u)(b.y),defaultValues:{message:""}}),P=()=>{var e;null===(e=s.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})};return(0,u.useEffect)(()=>{if(i){let e=new WebSocket("ws://localhost:8000/ws/chat/?token=".concat(i));return c.current=e,e.onopen=()=>{console.log("WebSocket connected")},e.onmessage=e=>{let s;try{s=JSON.parse(e.data)}catch(s){console.error("Received non-JSON message:",e.data);return}console.log(s),n(e=>[{id:Math.random(),sender:s.user,body:s.message,chat_room:"",created_at:new Date().toISOString()},...e])},e.onclose=()=>{console.log("WebSocket disconnected")},(0,j.G)("/chats/messages/",null,"GET",i).then(e=>e.json()).then(e=>{n(e.results),P()}),()=>{e.close()}}},[i]),(0,u.useEffect)(()=>{P()},[a]),t?(0,r.jsxs)("div",{className:"h-[500px] w-[400px] bg-slate-200 fixed bottom-0 right-0 rounded-xl overflow-clip flex flex-col z-50 shadow-lg",children:[(0,r.jsxs)("header",{className:"flex justify-between items-center bg-blue-600 text-white py-3 px-2",children:[(0,r.jsx)("h2",{className:"font-semibold text-lg",children:"خدمة العملاء"}),(0,r.jsx)(h.T,{className:"bg-black/75 p-1 rounded-full min-w-8",onPress:()=>l(!1),children:(0,r.jsx)(N.A,{className:"text-white size-4"})})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto px-2 py-3",children:0===a.length?(0,r.jsx)("div",{className:"grid place-content-center h-full",children:(0,r.jsx)("p",{children:"لا يوجد لديك رسائل"})}):(0,r.jsxs)("div",{className:"flex flex-col-reverse gap-2",children:[a.map(e=>(0,r.jsxs)("div",{className:(0,j.cn)("flex flex-col mb-1",e.sender===d?"items-end":"items-start"),children:[(0,r.jsx)("p",{className:(0,j.cn)("max-w-[250px] px-4 py-2 rounded-xl text-white text-sm",e.sender===d?"bg-red-500 rounded-tl-none":"bg-black/25 rounded-tr-none"),children:e.body}),(0,r.jsx)("span",{className:"text-xs mt-1 block text-right text-gray-500",children:(0,j.f)(e.created_at)})]},e.id)),(0,r.jsx)("div",{ref:s})]})}),(0,r.jsxs)("form",{className:"flex gap-2 px-2 border-t border-gray-300 pt-3 pb-2",onSubmit:m(e=>{var s;(null===(s=c.current)||void 0===s?void 0:s.readyState)===WebSocket.OPEN&&(c.current.send(JSON.stringify({message:e.message})),T())}),children:[(0,r.jsx)(S.xI,{name:"message",control:x,render:e=>{var s;let{field:t}=e;return(0,r.jsx)(v.r,{...t,placeholder:"أدخل رسالتك",isInvalid:!!g.message,errorMessage:null===(s=g.message)||void 0===s?void 0:s.message})}}),(0,r.jsx)(h.T,{type:"submit",disabled:k,className:"min-w-8 bg-blue-600",children:(0,r.jsx)(y.A,{className:"text-white"})})]})]}):(0,r.jsx)(h.T,{onPress:()=>l(!0),className:"fixed bottom-4 right-4 w-16 h-16 px-0 bg-blue-600 text-white rounded-full text-xl font-bold grid place-content-center z-40 shadow-lg",children:(0,r.jsx)(w.A,{className:"size-8"})})}let P=(0,k.default)(()=>Promise.resolve(T),{ssr:!1});var A=t(76046),_=t(30814);let E="force-dynamic",C=(0,u.createContext)(null);function G(e){let{children:s}=e,[t,l]=(0,u.useState)(!0),[a,n]=(0,u.useState)(!1),[c,i]=(0,u.useState)(null),[o]=(0,f.lT)(),d=(0,A.useSearchParams)(),h=null==d?void 0:d.get("error");return(0,u.useEffect)(()=>{"not-logged-in"===h&&_.o.error("يجب عليك تسجيل الدخول للوصول إلى هذه الصفحة")},[h]),(0,u.useEffect)(()=>{(0,j.G)("/auth/jwt/verify/",{token:o.access},"POST").then(e=>{200===e.status?(n(!0),(0,j.G)("/users/me/",null,"GET",o.access).then(e=>e.json()).then(e=>{i(e),l(!1)})):(n(!1),l(!1))}).then(()=>{})},[o]),(0,r.jsx)(C.Provider,{value:{isValid:a,isLoading:t,user:c},children:(0,r.jsxs)("div",{className:"flex flex-col min-h-screen relative",children:[(0,r.jsx)(g,{}),(0,r.jsx)("main",{className:"flex-1 ",children:s}),(0,r.jsx)(x,{}),c&&!(null==c?void 0:c.is_admin)&&(0,r.jsx)(P,{})]})})}function I(e){let{children:s}=e;return(0,r.jsx)(u.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(G,{children:s})})}},21567:(e,s,t)=>{t.d(s,{G:()=>n,cn:()=>a,f:()=>c});var r=t(43463),l=t(69795);function a(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.QP)((0,r.$)(s))}async function n(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",r=arguments.length>3?arguments[3]:void 0;try{let l=await fetch("http://localhost:8000"+e,{method:t,body:null===s?null:JSON.stringify(s),headers:{"Content-Type":"application/json",Authorization:r?"Bearer ".concat(r):""}});if(!l.ok)return console.error("API error: ".concat(l.status," ").concat(l.statusText)),{json:()=>Promise.resolve({error:!0,status:l.status,message:"API error: ".concat(l.status," ").concat(l.statusText),results:[]})};return l}catch(e){return console.error("Fetch error:",e),{json:()=>Promise.resolve({error:!0,message:e instanceof Error?e.message:"Unknown error",results:[]})}}}let c=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},1063:(e,s,t)=>{t.d(s,{y:()=>l});var r=t(10842);let l=r.Ik({message:r.Yj().min(1,"يرجى إدخال رسالة")})}}]);