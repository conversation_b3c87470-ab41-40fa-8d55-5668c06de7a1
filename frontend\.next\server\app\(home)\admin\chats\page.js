(()=>{var e={};e.id=517,e.ids=[517],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},66324:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>p,tree:()=>d});var n=r(70260),o=r(28203),i=r(25155),a=r.n(i),s=r(67292),u={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>s[e]);r.d(t,u);let d=["",{children:["(home)",{children:["admin",{children:["chats",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,20774)),"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,47649)),"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,36953)),"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(home)/admin/chats/page",pathname:"/admin/chats",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79848:(e,t,r)=>{Promise.resolve().then(r.bind(r,20774))},43400:(e,t,r)=>{Promise.resolve().then(r.bind(r,34306))},96487:()=>{},78335:()=>{},34306:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,dynamic:()=>o});var n=r(45512);r(58009),r(91542);let o="force-dynamic";function i(){return(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{children:"Admin Chats Page"}),(0,n.jsx)("p",{children:"This is a test page to debug the build issue."})]})}},31831:(e,t,r)=>{"use strict";var n=r(67359);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},11271:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return i}});let n=r(39274),o=r(26552);function i(e){return(0,o.isRedirectError)(e)||(0,n.isNotFoundError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return s},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unstable_rethrow:function(){return i.unstable_rethrow}});let n=r(26552),o=r(39274),i=r(51370);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class s extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11541:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26552:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return s},getRedirectStatusCodeFromError:function(){return f},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return l},isRedirectError:function(){return c},permanentRedirect:function(){return d},redirect:function(){return u}});let o=r(19121),i=r(11541),a="NEXT_REDIRECT";function s(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let n=Error(a);return n.digest=a+";"+t+";"+e+";"+r+";",n}function u(e,t){let r=o.actionAsyncStorage.getStore();throw s(e,t||((null==r?void 0:r.isAction)?"push":"replace"),i.RedirectStatusCode.TemporaryRedirect)}function d(e,t){throw void 0===t&&(t="replace"),s(e,t,i.RedirectStatusCode.PermanentRedirect)}function c(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,o=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===a&&("replace"===n||"push"===n)&&"string"==typeof o&&!isNaN(s)&&s in i.RedirectStatusCode}function l(e){return c(e)?e.digest.split(";").slice(2,-2).join(";"):null}function p(e){if(!c(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function f(e){if(!c(e))throw Error("Not a redirect error");return Number(e.digest.split(";").at(-2))}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51370:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,a.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,n.isDynamicUsageError)(t)||(0,o.isPostpone)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(62349),o=r(67418),i=r(40627),a=r(11271);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62349:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicUsageError",{enumerable:!0,get:function(){return s}});let n=r(42490),o=r(40627),i=r(11271),a=r(10436),s=e=>(0,n.isDynamicServerError)(e)||(0,o.isBailoutToCSRError)(e)||(0,i.isNextRouterError)(e)||(0,a.isDynamicPostpone)(e)},67418:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isPostpone",{enumerable:!0,get:function(){return n}});let r=Symbol.for("react.postpone");function n(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}},40627:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return o}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},20774:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,dynamic:()=>o});var n=r(46760);let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx","dynamic"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (3)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\admin\\\\chats\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (3)\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx","default")},47649:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,dynamic:()=>s});var n=r(62740);r(76301);var o=r(31831),i=r(43327),a=r(44512);let s="force-dynamic";async function u({children:e}){console.log("Hitted");let t=(await (0,a.UL)()).get("access");if(!t?.value)return(0,o.notFound)();let r=await (0,i.G)("/users/me/",null,"GET",t.value);return 200===r.status&&(await r.json()).is_admin?(0,n.jsx)(n.Fragment,{children:e}):(0,o.notFound)()}},43327:(e,t,r)=>{"use strict";async function n(e,t,r="GET",o){try{let n=await fetch("http://localhost:8000"+e,{method:r,body:null===t?null:JSON.stringify(t),headers:{"Content-Type":"application/json",Authorization:o?`Bearer ${o}`:""}});if(!n.ok)return console.error(`API error: ${n.status} ${n.statusText}`),{json:()=>Promise.resolve({error:!0,status:n.status,message:`API error: ${n.status} ${n.statusText}`,results:[]})};return n}catch(e){return console.error("Fetch error:",e),{json:()=>Promise.resolve({error:!0,message:e instanceof Error?e.message:"Unknown error",results:[]})}}}r.d(t,{G:()=>n})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[989,368,944,512,956,263,597],()=>r(66324));module.exports=n})();